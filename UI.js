// ==UserScript==
// @name         edvinity Settings Panel
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Modern vertical settings panel with sleek design
// <AUTHOR>
// @match        *://*/*
// @run-at       document-end
// ==/UserScript==

(() => {
  'use strict';

  // Check if we're in an iframe - only run the full UI in the main window
  const isInIframe = window !== window.top;

  // Create a global identifier to prevent multiple instances
  if (window.edvinityInitialized) {
    return;
  }

  // Mark as initialized
  window.edvinityInitialized = true;

  // Default theme colors (red theme)
  const DEFAULT_THEME = {
    PRIMARY: '#FF2D55',
    PRIMARY_HOVER: '#FF0A3A',
    PRIMARY_ACTIVE: '#D10930'
  };



  const BASE_COLORS = {
    BG_DARK: '#121212',
    BG_CARD: '#1E1E1E',
    BG_CARD_HOVER: '#252525',
    BG_CARD_ACTIVE: '#2A2A2A',
    TEXT_PRIMARY: '#FFFFFF',
    TEXT_SECONDARY: '#AAAAAA',
    TEXT_MUTED: '#777777',
    BORDER: '#333333',
    BORDER_LIGHT: '#2A2A2A',
    SHADOW: '0 8px 16px rgba(0, 0, 0, 0.4)',
    SHADOW_SMALL: '0 4px 8px rgba(0, 0, 0, 0.3)',
    TOGGLE_BG_OFF: '#444444',
    TOGGLE_HANDLE: '#FFFFFF',
  };

  const COLORS = {
    ...BASE_COLORS,
    ...DEFAULT_THEME,
    TOGGLE_BG_ON: DEFAULT_THEME.PRIMARY
  };

  const FONTS = {
    SYSTEM: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
    SIZE_LARGE: '18px',
    SIZE_MEDIUM: '15px',
    SIZE_SMALL: '13px',
    SIZE_TINY: '11px',
    WEIGHT_REGULAR: '400',
    WEIGHT_MEDIUM: '500',
    WEIGHT_BOLD: '700',
    LETTER_SPACING: '0.3px',
  };

  const Z_INDEX = {
    BUTTON: 9990,
    BACKDROP: 9995,
    PANEL: 10000,
  };

  const SETTINGS = [
    {
      id: 'appearance-section',
      title: 'Appearance',
      icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M12 1v6m0 6v6"></path><path d="m4.2 4.2 4.2 4.2m5.6 5.6 4.2 4.2"></path><path d="M1 12h6m6 0h6"></path><path d="m4.2 19.8 4.2-4.2m5.6-5.6 4.2-4.2"></path></svg>',
      items: [
        { id: 'background-blur', title: 'Background Blur', description: 'Enable blur effect behind the settings panel', type: 'toggle', value: true },
        { id: 'click-to-close', title: 'Click to Close', description: 'Close the panel when clicking outside of it', type: 'toggle', value: true },
        { id: 'placeholder-2', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: true },
        { id: 'placeholder-3', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: false }
      ]
    },

    {
      id: 'features-section',
      title: 'Features',
      icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line></svg>',
      items: [
        { id: 'placeholder-11', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: true },
        { id: 'placeholder-12', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: false },
        { id: 'placeholder-13', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: true }
      ]
    },

    {
      id: 'privacy-section',
      title: 'Privacy',
      icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>',
      items: [
        {
          id: 'blur-info',
          title: 'Blur Info',
          description: 'Blur sensitive information like student IDs, names, and personal data',
          type: 'toggle-dropdown',
          value: false,
          children: [
            {
              id: 'blur-intensity',
              title: 'Blur Intensity',
              description: 'Adjust the strength of the blur effect',
              type: 'select',
              value: 'medium',
              options: [
                { value: 'low', label: 'Low (3px)' },
                { value: 'medium', label: 'Medium (6px)' },
                { value: 'high', label: 'High (10px)' }
              ]
            },
            {
              id: 'name-spoofer',
              title: 'Name Spoofer',
              description: 'Replace names with custom text or presets',
              type: 'toggle-dropdown',
              value: false,
              children: [
                {
                  id: 'spoof-mode',
                  title: 'Spoof Mode',
                  description: 'Choose between custom text or preset options',
                  type: 'select',
                  value: 'preset',
                  options: [
                    { value: 'preset', label: 'Use Preset' },
                    { value: 'custom', label: 'Custom Text' }
                  ]
                },
                {
                  id: 'preset-names',
                  title: 'Preset Names',
                  description: 'Select from predefined name replacements',
                  type: 'select',
                  value: 'student',
                  options: [
                    { value: 'student', label: 'Student' },
                    { value: 'user', label: 'User' },
                    { value: 'anonymous', label: 'Anonymous' },
                    { value: 'redacted', label: '[REDACTED]' }
                  ]
                },
                {
                  id: 'custom-name',
                  title: 'Custom Name',
                  description: 'Enter your own replacement text',
                  type: 'input',
                  value: 'Anonymous',
                  placeholder: 'Enter custom name...'
                }
              ]
            },
            {
              id: 'blur-profile-pics',
              title: 'Blur Profile Pictures',
              description: 'Apply blur effect to profile images',
              type: 'toggle',
              value: false
            },
            {
              id: 'blur-student-ids',
              title: 'Blur Student IDs',
              description: 'Hide student identification numbers',
              type: 'toggle',
              value: false
            }
          ]
        },
        {
          id: 'auto-vocab',
          title: 'Auto Vocabulary',
          description: 'Automatically complete vocabulary activities and exercises',
          type: 'toggle-dropdown',
          value: false,
          children: [
            {
              id: 'vocab-auto-fill',
              title: 'Auto Fill Text',
              description: 'Automatically fill vocabulary text boxes with correct answers',
              type: 'toggle',
              value: true
            },
            {
              id: 'vocab-auto-play',
              title: 'Auto Play Audio',
              description: 'Automatically click play buttons for vocabulary audio',
              type: 'toggle',
              value: true
            },
            {
              id: 'vocab-auto-submit',
              title: 'Auto Submit',
              description: 'Automatically submit vocabulary activities when complete',
              type: 'toggle',
              value: true
            },
            {
              id: 'vocab-delay',
              title: 'Action Delay',
              description: 'Delay between vocabulary actions (in milliseconds)',
              type: 'select',
              value: '1000',
              options: [
                { value: '500', label: '0.5 seconds' },
                { value: '1000', label: '1 second' },
                { value: '1500', label: '1.5 seconds' },
                { value: '2000', label: '2 seconds' }
              ]
            }
          ]
        },
        { id: 'show-column', title: 'Show Column', description: 'Show sample responses and example answers in activities', type: 'toggle', value: false },
        { id: 'anti-logout', title: 'Anti-Logout', description: 'Prevent being logged out due to inactivity', type: 'toggle', value: false },
        { id: 'auto-mute', title: 'Auto-Mute', description: 'Automatically mute all audio on the page', type: 'toggle', value: false }
      ]
    },

    {
      id: 'account-section',
      title: 'Account',
      icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>',
      items: [
        { id: 'placeholder-20', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: true },
        { id: 'placeholder-21', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: false },
        { id: 'placeholder-22', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: false }
      ]
    },

    {
      id: 'advanced-section',
      title: 'Advanced',
      icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>',
      items: [
        { id: 'placeholder-23', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: false },
        { id: 'placeholder-24', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: false },
        { id: 'placeholder-25', title: 'Placeholder', description: 'Placeholder', type: 'toggle', value: true }
      ]
    },

    {
      id: 'shortcuts-section',
      title: 'Shortcuts',
      icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 12l2 2 4-4"></path><path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path><path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path><path d="M12 21c0-1-1-3-3-3s-3 2-3 3 1 3 3 3 3-2 3-3"></path><path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path></svg>',
      items: [
        { id: 'toggle-panel-hotkey', title: 'Toggle Panel', description: 'Customize hotkey for toggling the settings panel', type: 'hotkey', value: 'Escape' },
        { id: 'tabs-mode-hotkey', title: 'Switch to Tabs Mode', description: 'Customize hotkey for switching to tabs mode', type: 'hotkey', value: 'Ctrl+1' },
        { id: 'regular-mode-hotkey', title: 'Switch to Regular Mode', description: 'Customize hotkey for switching to regular mode', type: 'hotkey', value: 'Ctrl+2' },
        { id: 'next-tab-hotkey', title: 'Next Tab', description: 'Customize hotkey for navigating to next tab', type: 'hotkey', value: 'Alt+ArrowRight' },
        { id: 'prev-tab-hotkey', title: 'Previous Tab', description: 'Customize hotkey for navigating to previous tab', type: 'hotkey', value: 'Alt+ArrowLeft' }
      ]
    }
  ];

  const css = `
    /* CSS Variables for easy theming */
    :root {
      --panel-width-tabs: 420px;
      --panel-width-regular: 320px; /* Increased from 240px to be less skinny */
      --panel-width: var(--panel-width-tabs); /* Default to tabs mode width */
    }

    /* Custom scrollbar styles */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: \${COLORS.BG_DARK};
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: \${COLORS.BORDER};
      border-radius: 4px;
      border: 2px solid \${COLORS.BG_DARK};
    }

    ::-webkit-scrollbar-thumb:hover {
      background: \${COLORS.PRIMARY}80;
    }

    ::-webkit-scrollbar-corner {
      background: \${COLORS.BG_DARK};
    }

    /* Firefox scrollbar */
    * {
      scrollbar-width: thin;
      scrollbar-color: \${COLORS.BORDER} \${COLORS.BG_DARK};
    }

    /* ONLY target the specific nested dropdown line issue */
    #dropdown-blur-info #dropdown-name-spoofer,
    #setting-name-spoofer + #dropdown-name-spoofer {
      border: none !important;
      box-shadow: none !important;
    }

    @keyframes edvinityPulse {
      0% { box-shadow: 0 0 0 0 rgba(255, 45, 85, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(255, 45, 85, 0); }
      100% { box-shadow: 0 0 0 0 rgba(255, 45, 85, 0); }
    }

    @keyframes edvinityPanelEnter {
      0% { transform: scale(0.9); opacity: 0; }
      100% { transform: scale(1); opacity: 1; }
    }

    @keyframes edvinityPanelExit {
      0% { transform: scale(1); opacity: 1; }
      100% { transform: scale(0.9); opacity: 0; }
    }

    @keyframes edvinityWindowExit {
      0% { transform: scale(1); opacity: 1; }
      100% { transform: scale(0.8); opacity: 0; }
    }

    @keyframes edvinityButtonAppear {
      0% { opacity: 0; transform: translateY(15px); }
      70% { opacity: 1; transform: translateY(-3px); }
      85% { transform: translateY(1px); }
      100% { opacity: 1; transform: translateY(0); }
    }

    .edvinity-button-container {
      position: fixed;
      bottom: 25px;
      right: 25px;
      z-index: 9990;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: edvinityButtonAppear 0.7s cubic-bezier(0.19, 1, 0.22, 1) forwards;
      filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
    }

    .edvinity-button {
      height: 50px;
      min-width: 50px;
      border-radius: 14px;
      background: #1E1E1E;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25), 0 0 1px rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 45, 85, 0.2);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1),
                  transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
                  box-shadow 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
                  min-width 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
      overflow: hidden;
      padding: 0 16px;
      will-change: transform, min-width, box-shadow;
    }

    .edvinity-button-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      position: relative;
      padding: 0 2px;
      transform: translateZ(0); /* Force hardware acceleration for smoother animations */
    }

    .edvinity-button-icon {
      width: 24px;
      height: 24px;
      stroke: #FFFFFF;
      stroke-width: 1.75;
      transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
                  stroke 0.4s cubic-bezier(0.19, 1, 0.22, 1);
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      will-change: transform, stroke;
    }

    .edvinity-button-text {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      font-size: 13px;
      font-weight: 500;
      color: #FFFFFF;
      white-space: nowrap;
      opacity: 0;
      max-width: 0;
      overflow: hidden;
      margin-left: 0;
      transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1),
                  max-width 0.5s cubic-bezier(0.19, 1, 0.22, 1),
                  margin-left 0.5s cubic-bezier(0.19, 1, 0.22, 1);
      letter-spacing: 0.3px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      will-change: opacity, max-width, margin-left;
    }

    .edvinity-button::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08),
        rgba(255, 255, 255, 0) 100%);
      border-radius: 14px;
      z-index: 0;
    }

    /* Expanded state */
    .edvinity-button.expanded {
      min-width: 130px;
      background: #252525;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 0 1px rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 45, 85, 0.3);
    }

    .edvinity-button.expanded .edvinity-button-icon {
      stroke: #FF2D55;
      transition: stroke 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    }

    .edvinity-button.expanded .edvinity-button-text {
      opacity: 1;
      max-width: 100px;
      margin-left: 12px;
      transition-delay: 0.05s;
    }

    .edvinity-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.35), 0 0 0 1px rgba(255, 45, 85, 0.3);
      border-color: rgba(255, 45, 85, 0.4);
      transition-duration: 0.4s;
    }

    .edvinity-button:not(.expanded):hover .edvinity-button-icon {
      transform: scale(1.15);
      stroke: #FF2D55;
      transition-duration: 0.3s;
    }

    .edvinity-button:active {
      transform: translateY(-1px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 45, 85, 0.4);
      background: linear-gradient(135deg,
        #252525,
        rgba(255, 45, 85, 0.15)
      );
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .edvinity-backdrop {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.75);
      backdrop-filter: blur(6px);
      -webkit-backdrop-filter: blur(6px);
      z-index: ${Z_INDEX.BACKDROP};
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1),
                  visibility 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    }

    .edvinity-backdrop.visible {
      opacity: 1;
      visibility: visible;
    }

    .edvinity-backdrop.no-blur {
      opacity: 0 !important;
      visibility: hidden !important;
      pointer-events: none !important;
    }

    .edvinity-panel {
      position: fixed;
      top: 50%;
      left: 50%;
      width: 440px;
      height: 580px;
      max-height: 80vh;
      margin-left: -220px; /* Half the width */
      margin-top: -290px; /* Half the height */
      opacity: 0;
      background-color: ${COLORS.BG_DARK};
      background-image:
        radial-gradient(
          circle at 100% 0%,
          rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.08) 0%,
          transparent 70%
        ),
        linear-gradient(
          to bottom,
          rgba(30, 30, 30, 0.5) 0%,
          rgba(18, 18, 18, 1) 100%
        );
      box-shadow: ${COLORS.SHADOW}, 0 10px 30px rgba(0, 0, 0, 0.4);
      z-index: ${Z_INDEX.PANEL};
      display: flex;
      flex-direction: column;
      overflow: hidden;
      transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1),
                  height 0.3s cubic-bezier(0.19, 1, 0.22, 1),
                  width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
                  margin-left 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
                  transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
                  visibility 0.4s cubic-bezier(0.19, 1, 0.22, 1);
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      border-radius: 12px;
      user-select: none;
      transform: scale(0.95);
      will-change: width, margin-left, transform;
      visibility: hidden; /* Hide completely when not visible */
      pointer-events: none; /* Disable interactions when hidden */
    }

    .edvinity-panel.closing {
      pointer-events: none; /* Prevent interactions during closing animation */
    }

    /* Panel mode specific styles */
    .edvinity-panel.edvinity-panel-mode {
      width: var(--panel-width-regular) !important; /* Make it significantly skinnier and use !important to override any inline styles */
      margin-left: calc(var(--panel-width-regular) / -2) !important; /* Half the width */
      transition: width 0.3s ease-in-out, margin-left 0.3s ease-in-out; /* Add smooth transition */
    }

    .edvinity-panel.edvinity-panel-mode .edvinity-content {
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      padding: 0 10px;
      /* Add scrollbar styling */
      scrollbar-width: thin;
      scrollbar-color: ${COLORS.BORDER} ${COLORS.BG_DARK};
    }

    /* Apply the global scrollbar styles */
    .edvinity-panel.edvinity-panel-mode .edvinity-content::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    .edvinity-panel.edvinity-panel-mode .edvinity-content::-webkit-scrollbar-track {
      background: ${COLORS.BG_DARK};
      border-radius: 4px;
    }

    .edvinity-panel.edvinity-panel-mode .edvinity-content::-webkit-scrollbar-thumb {
      background: ${COLORS.BORDER};
      border-radius: 4px;
      border: 2px solid ${COLORS.BG_DARK};
    }

    .edvinity-panel.edvinity-panel-mode .edvinity-content::-webkit-scrollbar-thumb:hover {
      background: ${COLORS.PRIMARY}80;
    }

    .edvinity-panel.edvinity-panel-mode .edvinity-section {
      margin: 12px 0;
      padding: 0;
    }

    /* Tabs mode specific styles */
    .edvinity-panel.edvinity-tabs-mode {
      width: var(--panel-width-tabs) !important; /* Use !important to ensure it overrides any inline styles */
      margin-left: calc(var(--panel-width-tabs) / -2) !important; /* Half the width */
      transition: width 0.3s ease-in-out, margin-left 0.3s ease-in-out; /* Add smooth transition */
    }

    /* Floating mode specific styles */
    .edvinity-panel.edvinity-floating-mode {
      width: 420px;
      height: 560px;
      margin-left: -210px; /* Half the width */
      margin-top: -280px; /* Half the height */
      background-color: ${COLORS.BG_DARK};
      background-image:
        linear-gradient(to bottom,
          rgba(40, 40, 40, 0.5),
          rgba(20, 20, 20, 0.8)
        );
    }

    .edvinity-panel.edvinity-floating-mode .edvinity-content {
      display: flex;
      flex-direction: column;
      padding: 0;
      overflow: hidden;
    }

    /* Floating windows */
    .edvinity-floating-window {
      position: fixed;
      background-color: ${COLORS.BG_DARK};
      background-image:
        radial-gradient(
          circle at 100% 0%,
          rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.08) 0%,
          transparent 70%
        ),
        linear-gradient(
          to bottom,
          rgba(30, 30, 30, 0.5) 0%,
          rgba(18, 18, 18, 1) 100%
        );
      border-radius: 12px;
      box-shadow: ${COLORS.SHADOW}, 0 10px 30px rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      overflow: hidden;
      z-index: 9980;
      width: 360px;
      height: 420px;
      animation: edvinityFloatingWindowEnter 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) both;
      opacity: 0.95;
      transition: opacity 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
      animation-delay: calc(var(--window-index, 0) * 0.1s);
    }

    .edvinity-floating-window.closing {
      animation: edvinityWindowExit 0.3s cubic-bezier(0.19, 1, 0.22, 1) forwards;
      pointer-events: none;
    }

    .edvinity-floating-window:hover {
      opacity: 1;
      box-shadow: ${COLORS.SHADOW}, 0 15px 40px rgba(0, 0, 0, 0.6);
      transform: translateY(-2px);
    }

    .edvinity-floating-window.active {
      z-index: 9985;
      box-shadow: 0 0 0 2px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.5), ${COLORS.SHADOW}, 0 15px 40px rgba(0, 0, 0, 0.6);
    }

    .edvinity-floating-window.pulse-highlight {
      animation: edvinityWindowPulse 1s cubic-bezier(0.19, 1, 0.22, 1);
    }

    @keyframes edvinityWindowPulse {
      0% { box-shadow: 0 0 0 0 rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.5), ${COLORS.SHADOW}, 0 15px 40px rgba(0, 0, 0, 0.6); }
      50% { box-shadow: 0 0 0 10px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0), ${COLORS.SHADOW}, 0 15px 40px rgba(0, 0, 0, 0.6); }
      100% { box-shadow: 0 0 0 0 rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0), ${COLORS.SHADOW}, 0 15px 40px rgba(0, 0, 0, 0.6); }
    }

    @keyframes edvinityFloatingWindowEnter {
      0% {
        transform: scale(0.8) translateY(20px);
        opacity: 0;
        filter: blur(10px);
      }
      70% {
        transform: scale(1.03) translateY(-5px);
        opacity: 0.95;
        filter: blur(0px);
      }
      100% {
        transform: scale(1) translateY(0);
        opacity: 0.95;
        filter: blur(0px);
      }
    }

    @keyframes edvinityFloatingWindowExit {
      0% {
        transform: scale(1) translateY(0);
        opacity: 0.95;
        filter: blur(0px);
      }
      100% {
        transform: scale(0.8) translateY(20px);
        opacity: 0;
        filter: blur(10px);
      }
    }

    .edvinity-floating-window.closing {
      animation: edvinityWindowExit 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
      pointer-events: none;
    }

    /* Floating window transition styles */
    .edvinity-floating-window {
      transition: height 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      will-change: height;
    }

    /* Minimized window styles */
    .edvinity-floating-window[data-minimized="true"] {
      overflow: hidden;
    }

    /* Content transition */
    .edvinity-floating-content {
      transition: opacity 0.2s ease;
      will-change: opacity;
    }

    /* Animation state */
    .edvinity-floating-window.animating {
      pointer-events: none;
    }

    .edvinity-floating-window.animating .edvinity-floating-header {
      pointer-events: auto;
    }

    /* Ensure header is always clickable */
    .edvinity-floating-header {
      pointer-events: auto;
    }

    .edvinity-floating-header {
      padding: 18px 20px;
      background: linear-gradient(to bottom, rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.08), transparent);
      border-bottom: 1px solid ${COLORS.BORDER};
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: move;
      position: relative;
      height: 65px;
      box-sizing: border-box;
    }

    .edvinity-floating-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(to right,
        transparent,
        rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3),
        transparent
      );
    }

    .edvinity-floating-title {
      font-family: ${FONTS.SYSTEM};
      font-size: 17px;
      font-weight: ${FONTS.WEIGHT_BOLD};
      color: ${COLORS.TEXT_PRIMARY};
      display: flex;
      align-items: center;
      gap: 12px;
      position: relative;
      padding-left: 14px;
      letter-spacing: 0.3px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .edvinity-floating-title::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      background: ${COLORS.PRIMARY};
      border-radius: 2px;
      box-shadow: 0 0 8px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.5);
    }

    .edvinity-floating-title-icon {
      width: 20px;
      height: 20px;
      color: ${COLORS.PRIMARY};
      filter: drop-shadow(0 0 2px ${COLORS.PRIMARY}40);
    }

    .edvinity-floating-controls {
      display: flex;
      gap: 12px;
    }

    .edvinity-floating-control {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.1);
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    @keyframes edvinityControlHover {
      0% { transform: scale(1); }
      50% { transform: scale(1.15); }
      100% { transform: scale(1.1); }
    }

    .edvinity-floating-control svg {
      width: 16px;
      height: 16px;
      stroke: ${COLORS.TEXT_SECONDARY};
      transition: stroke 0.2s ease, transform 0.3s ease;
    }

    .edvinity-floating-control:hover {
      background-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2);
      transform: scale(1.1);
      box-shadow: 0 0 8px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      animation: edvinityControlHover 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .edvinity-floating-control:hover svg {
      stroke: ${COLORS.TEXT_PRIMARY};
      transform: rotate(15deg);
    }

    .edvinity-floating-close:hover {
      background-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
    }

    .edvinity-floating-close:hover svg {
      stroke: white;
      transform: rotate(90deg);
    }

    .edvinity-floating-minimize:hover svg {
      transform: rotate(180deg);
    }

    /* Minimize icon transition */
    .edvinity-floating-minimize svg {
      transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .edvinity-floating-close:hover svg {
      stroke: white;
    }

    .edvinity-floating-control:active {
      transform: scale(0.95);
      transition-duration: 0.1s;
    }

    .edvinity-floating-control svg {
      width: 18px;
      height: 18px;
      stroke: ${COLORS.PRIMARY};
      stroke-width: 2;
      filter: drop-shadow(0 0 1px ${COLORS.PRIMARY}40);
    }

    /* Global scrollbar styles - applied to specific elements */
    .edvinity-scrollbar {
      scrollbar-width: thin;
      scrollbar-color: ${COLORS.BORDER} ${COLORS.BG_DARK};
    }

    .edvinity-scrollbar::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    .edvinity-scrollbar::-webkit-scrollbar-track {
      background: ${COLORS.BG_DARK};
      border-radius: 4px;
    }

    .edvinity-scrollbar::-webkit-scrollbar-thumb {
      background: ${COLORS.BORDER};
      border-radius: 4px;
      border: 2px solid ${COLORS.BG_DARK};
    }

    .edvinity-scrollbar::-webkit-scrollbar-thumb:hover {
      background: ${COLORS.PRIMARY}80;
    }

    .edvinity-scrollbar::-webkit-scrollbar-corner {
      background: ${COLORS.BG_DARK};
    }

    .edvinity-floating-content {
      padding: 22px;
      overflow-y: auto;
      height: calc(100% - 65px);
      box-sizing: border-box;
      /* Add scrollbar styling */
      scrollbar-width: thin;
      scrollbar-color: ${COLORS.BORDER} ${COLORS.BG_DARK};
    }

    /* Apply the global scrollbar styles */
    .edvinity-floating-content::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    .edvinity-floating-content::-webkit-scrollbar-track {
      background: ${COLORS.BG_DARK};
      border-radius: 4px;
    }

    .edvinity-floating-content::-webkit-scrollbar-thumb {
      background: ${COLORS.BORDER};
      border-radius: 4px;
      border: 2px solid ${COLORS.BG_DARK};
    }

    .edvinity-floating-content::-webkit-scrollbar-thumb:hover {
      background: ${COLORS.PRIMARY}80;
    }

    /* Style items inside floating windows to match the main UI */
    .edvinity-floating-window .edvinity-items {
      display: flex;
      flex-direction: column;
      gap: 14px;
    }

    .edvinity-floating-window .edvinity-item {
      background-color: ${COLORS.BG_CARD};
      border-radius: 12px;
      border: 1px solid ${COLORS.BORDER_LIGHT};
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .edvinity-floating-window .edvinity-item:hover {
      background-color: ${COLORS.BG_CARD_HOVER};
      transform: translateY(-2px);
      box-shadow: ${COLORS.SHADOW_SMALL};
    }

    .edvinity-floating-window .edvinity-item-content {
      flex: 1;
      padding-right: 15px;
    }

    .edvinity-floating-window .edvinity-item-title {
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 5px;
      color: ${COLORS.TEXT_PRIMARY};
    }

    .edvinity-floating-window .edvinity-item-description {
      font-size: 13px;
      color: ${COLORS.TEXT_SECONDARY};
      line-height: 1.4;
    }

    /* Floating tabs layout */
    .edvinity-floating-tabs {
      display: flex;
      background-color: ${COLORS.BG_CARD};
      border-bottom: 1px solid ${COLORS.BORDER};
      overflow-x: auto;
      padding: 0 2px;
      /* Use the global scrollbar styles */
      scrollbar-width: thin;
      scrollbar-color: ${COLORS.BORDER} ${COLORS.BG_DARK};
    }

    /* Apply the global scrollbar styles */
    .edvinity-floating-tabs::-webkit-scrollbar {
      height: 8px;
      width: 8px;
    }

    .edvinity-floating-tabs::-webkit-scrollbar-track {
      background: ${COLORS.BG_DARK};
      border-radius: 4px;
    }

    .edvinity-floating-tabs::-webkit-scrollbar-thumb {
      background: ${COLORS.BORDER};
      border-radius: 4px;
      border: 2px solid ${COLORS.BG_DARK};
    }

    .edvinity-floating-tabs::-webkit-scrollbar-thumb:hover {
      background: ${COLORS.PRIMARY}80;
    }

    .edvinity-floating-tab {
      padding: 10px 14px;
      font-family: ${FONTS.SYSTEM};
      font-size: 13px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      color: ${COLORS.TEXT_SECONDARY};
      cursor: pointer;
      white-space: nowrap;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      margin: 4px 2px;
      border-radius: 6px;
    }

    .edvinity-floating-tab:hover {
      background-color: ${COLORS.BG_CARD_HOVER};
      color: ${COLORS.TEXT_PRIMARY};
      transform: translateY(-1px);
    }

    .edvinity-floating-tab.active {
      background-color: rgba(255, 45, 85, 0.15);
      color: ${COLORS.PRIMARY};
      box-shadow: 0 0 0 1px ${COLORS.PRIMARY}40;
    }

    .edvinity-floating-tab.active::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 2px;
      background-color: ${COLORS.PRIMARY};
      border-radius: 1px;
      box-shadow: 0 0 4px ${COLORS.PRIMARY}80;
    }

    .edvinity-floating-tab-content {
      display: none;
      padding: 15px;
      animation: edvinityFadeIn 0.3s ease forwards;
    }

    .edvinity-floating-tab-content.active {
      display: block;
    }

    /* Floating panel launcher */
    @keyframes edvinityFloatingItemAppear {
      0% {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
        filter: blur(5px);
      }
      70% {
        opacity: 1;
        transform: translateY(-5px) scale(1.02);
        filter: blur(0px);
      }
      100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0px);
      }
    }

    .edvinity-floating-launcher {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      padding: 30px;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .edvinity-floating-launcher-item {
      width: 110px;
      height: 110px;
      background-color: ${COLORS.BG_CARD};
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.1);
      border-radius: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
      padding: 15px;
      text-align: center;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      opacity: 0;
      animation: edvinityFloatingItemAppear 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    }

    .edvinity-floating-launcher-item::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08),
        rgba(255, 255, 255, 0.03) 50%,
        rgba(255, 255, 255, 0) 100%);
      border-radius: 12px;
      z-index: 0;
    }

    .edvinity-floating-launcher-item::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(to right,
        transparent,
        ${COLORS.PRIMARY}60,
        transparent
      );
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 1;
    }

    .edvinity-floating-launcher-item:hover {
      background-color: ${COLORS.BG_CARD_HOVER};
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
      border-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
    }

    .edvinity-floating-launcher-item:hover::after {
      opacity: 1;
    }

    .edvinity-floating-launcher-item:active {
      transform: translateY(-1px) scale(0.98);
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      background-color: ${COLORS.BG_CARD_ACTIVE};
    }

    @keyframes edvinityLauncherItemClick {
      0% { transform: translateY(-3px) scale(1); }
      40% { transform: translateY(-1px) scale(0.97); }
      70% { transform: translateY(-2px) scale(0.99); }
      100% { transform: translateY(-3px) scale(1); }
    }

    .edvinity-floating-launcher-item.clicked {
      animation: edvinityLauncherItemClick 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2);
    }

    .edvinity-floating-launcher-item.active {
      background: linear-gradient(135deg,
        ${COLORS.BG_CARD_HOVER},
        rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.15)
      );
      border-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      position: relative;
    }

    .edvinity-floating-launcher-item.active::after {
      opacity: 1;
      height: 3px;
      background: linear-gradient(to right,
        rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4),
        ${COLORS.PRIMARY},
        rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4)
      );
    }

    .edvinity-floating-launcher-item.active::before {
      background: linear-gradient(135deg,
        rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.08),
        rgba(255, 255, 255, 0.03) 50%,
        rgba(255, 255, 255, 0) 100%);
    }

    .edvinity-floating-launcher-item.minimized::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg,
        rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.05),
        rgba(255, 255, 255, 0.02) 50%,
        rgba(255, 255, 255, 0) 100%);
      border-radius: 12px;
      z-index: 0;
    }

    .edvinity-floating-launcher-item.minimized .edvinity-floating-launcher-icon {
      opacity: 0.7;
    }

    .edvinity-floating-launcher-item.minimized .edvinity-floating-launcher-title {
      opacity: 0.7;
    }

    .edvinity-floating-launcher-item.minimized::after {
      content: '';
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 3px;
      background-color: ${COLORS.PRIMARY}60;
      border-radius: 1.5px;
      box-shadow: 0 0 4px ${COLORS.PRIMARY}20;
      z-index: 2;
    }

    @keyframes edvinityIconPulse {
      0% { transform: scale(1); filter: drop-shadow(0 2px 4px rgba(255, 45, 85, 0.2)); }
      50% { transform: scale(1.15); filter: drop-shadow(0 4px 8px rgba(255, 45, 85, 0.4)); }
      100% { transform: scale(1); filter: drop-shadow(0 2px 4px rgba(255, 45, 85, 0.2)); }
    }

    @keyframes edvinityIconSpin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .edvinity-floating-launcher-icon {
      width: 36px;
      height: 36px;
      color: ${COLORS.TEXT_PRIMARY};
      margin-bottom: 14px;
      transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
                  color 0.3s cubic-bezier(0.19, 1, 0.22, 1),
                  filter 0.3s ease;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
      position: relative;
      z-index: 2;
    }

    .edvinity-floating-launcher-item:hover .edvinity-floating-launcher-icon {
      transform: scale(1.15);
      color: ${COLORS.PRIMARY};
      filter: drop-shadow(0 2px 4px rgba(255, 45, 85, 0.2));
    }

    .edvinity-floating-launcher-item.active .edvinity-floating-launcher-icon {
      color: ${COLORS.PRIMARY};
      filter: drop-shadow(0 2px 4px rgba(255, 45, 85, 0.3));
    }

    .edvinity-floating-launcher-item.clicked .edvinity-floating-launcher-icon {
      animation: edvinityIconSpin 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .edvinity-floating-launcher-title {
      font-family: ${FONTS.SYSTEM};
      font-size: 14px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      color: ${COLORS.TEXT_SECONDARY};
      letter-spacing: 0.3px;
      transition: color 0.3s ease, font-weight 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .edvinity-floating-launcher-item:hover .edvinity-floating-launcher-title {
      color: ${COLORS.TEXT_PRIMARY};
    }

    .edvinity-floating-launcher-item.active .edvinity-floating-launcher-title {
      color: ${COLORS.PRIMARY};
      font-weight: ${FONTS.WEIGHT_BOLD};
    }

    .edvinity-panel.visible {
      opacity: 1;
      transform: scale(1);
      visibility: visible;
      pointer-events: auto;
    }

    .edvinity-panel.dragging {
      transition: none;
      cursor: grabbing;
    }

    .edvinity-panel.minimized {
      height: 60px !important; /* Use !important to override inline styles */
      overflow: hidden;
    }

    .edvinity-panel.minimized .edvinity-content,
    .edvinity-panel.minimized .edvinity-footer {
      display: none;
    }

    /* Tabs Layout */
    .edvinity-tabs-layout {
      display: flex;
      flex: 1;
      overflow: hidden;
      position: relative;
    }

    .edvinity-tabs {
      width: 60px;
      background-color: rgba(0, 0, 0, 0.15);
      border-right: 1px solid ${COLORS.BORDER};
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      scrollbar-width: none;
      transition: width 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      position: relative;
      z-index: 2;
      align-items: center;
      padding: 5px 0;
    }

    .edvinity-tabs.expanded {
      width: 180px;
      align-items: stretch;
    }

    /* Fix dropdown positioning when sidebar is expanded */
    .edvinity-tabs.expanded ~ .edvinity-tab-content .edvinity-dropdown-content {
      max-width: calc(100% - 20px);
      width: calc(100% - 20px);
    }

    .edvinity-tabs::-webkit-scrollbar {
      display: none;
    }

    .edvinity-burger-toggle {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 60px; /* Position it at the edge of the tabs */
      width: 14px;
      height: 50px;
      background: rgba(0, 0, 0, 0.15); /* Match the tabs background */
      border-right: 1px solid ${COLORS.BORDER};
      border-top: 1px solid ${COLORS.BORDER};
      border-bottom: 1px solid ${COLORS.BORDER};
      border-left: none;
      border-radius: 0 4px 4px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 5; /* Modest z-index */
      transition: all 0.2s ease;
      overflow: hidden;
    }

    .edvinity-tabs.expanded + .edvinity-burger-toggle {
      left: 180px; /* Adjust position when tabs are expanded */
    }

    .edvinity-burger-toggle:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .edvinity-burger-toggle:active {
      background: rgba(255, 255, 255, 0.07);
    }

    .edvinity-burger-toggle svg {
      width: 10px;
      height: 10px;
      stroke: ${COLORS.TEXT_SECONDARY};
      stroke-width: 2;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      opacity: 0.7;
    }

    .edvinity-burger-toggle:hover svg {
      stroke: ${COLORS.TEXT_PRIMARY};
      opacity: 1;
      transform: translateX(-2px);
    }

    .edvinity-tabs.expanded + .edvinity-burger-toggle svg {
      transform: rotate(180deg);
    }

    .edvinity-tabs.expanded + .edvinity-burger-toggle:hover svg {
      transform: rotate(180deg) translateX(2px);
    }

    .edvinity-tab {
      padding: 14px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      position: relative;
      opacity: 0.7;
      margin: 4px 6px;
      overflow: hidden;
      border-radius: 8px;
      border: 1px solid transparent;
      text-align: center;
    }

    .edvinity-tabs.expanded .edvinity-tab {
      padding: 14px 12px;
      justify-content: flex-start;
      text-align: left;
    }

    .edvinity-tabs.expanded .edvinity-tab-icon {
      margin: 0;
    }

    .edvinity-tab:hover {
      background-color: rgba(255, 255, 255, 0.07);
      opacity: 0.95;
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .edvinity-tab.active {
      background-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.15);
      opacity: 1;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
    }

    .edvinity-tab-icon {
      width: 20px;
      height: 20px;
      min-width: 20px;
      color: ${COLORS.TEXT_PRIMARY};
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }

    .edvinity-tabs:not(.expanded) .edvinity-tab-icon {
      margin: 0 auto;
    }

    .edvinity-tab:hover .edvinity-tab-icon {
      transform: translateY(-2px);
      filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.3));
    }

    .edvinity-tab.active .edvinity-tab-icon {
      color: ${COLORS.PRIMARY};
    }

    .edvinity-tab-title {
      font-family: ${FONTS.SYSTEM};
      font-size: 13px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      color: ${COLORS.TEXT_PRIMARY};
      letter-spacing: 0.3px;
      white-space: nowrap;
      opacity: 0;
      max-width: 0;
      overflow: hidden;
      margin-left: 0;
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      transform: translateX(-10px);
      text-align: left;
      line-height: 1.2;
    }

    .edvinity-tabs.expanded .edvinity-tab-title {
      opacity: 1;
      max-width: 120px;
      margin-left: 12px;
      transform: translateX(0);
      display: flex;
      align-items: center;
    }

    .edvinity-tab.active .edvinity-tab-title {
      color: ${COLORS.PRIMARY};
      font-weight: ${FONTS.WEIGHT_BOLD};
    }

    .edvinity-tab-content {
      flex: 1;
      overflow-y: auto;
      padding: 20px 24px;
      display: none;
      box-sizing: border-box;
      width: 100%;
      text-align: left;
    }

    /* Adjust tab content when sidebar is expanded */
    .edvinity-tabs.expanded ~ .edvinity-tab-content {
      padding: 20px 15px;
    }

    .edvinity-tab-content.active {
      display: block;
      animation: edvinityTabContentEnter 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    }

    @keyframes edvinityFadeIn {
      0% { opacity: 0; transform: translateY(10px); }
      100% { opacity: 1; transform: translateY(0); }
    }

    @keyframes edvinityTabContentEnter {
      0% { opacity: 0; transform: translateX(20px); }
      100% { opacity: 1; transform: translateX(0); }
    }

    /* Removed resize handle styles for simplicity */

    .edvinity-header {
      padding: 14px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid ${COLORS.BORDER};
      background: linear-gradient(to bottom, rgba(255, 45, 85, 0.05), transparent);
      position: relative;
      cursor: grab;
    }

    .edvinity-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(to right,
        transparent,
        rgba(255, 45, 85, 0.3),
        transparent
      );
    }

    .edvinity-header.dragging {
      cursor: grabbing;
    }

    .edvinity-drag-handle {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .edvinity-title-container {
      display: flex;
      align-items: center;
      z-index: 2;
    }

    .edvinity-title {
      font-family: ${FONTS.SYSTEM};
      font-size: 16px;
      font-weight: ${FONTS.WEIGHT_BOLD};
      color: ${COLORS.TEXT_PRIMARY};
      letter-spacing: 0.3px;
      position: relative;
      padding-left: 10px;
    }

    .edvinity-title::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background: ${COLORS.PRIMARY};
      border-radius: 2px;
    }

    .edvinity-drag-indicator {
      display: flex;
      align-items: center;
      margin-right: 10px;
    }

    .edvinity-drag-indicator::before {
      content: '';
      width: 20px;
      height: 10px;
      background-image: radial-gradient(circle, ${COLORS.TEXT_SECONDARY} 1px, transparent 1.5px);
      background-size: 4px 4px;
      opacity: 0.6;
    }

    .edvinity-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 2;
    }

    .edvinity-minimize,
    .edvinity-close {
      width: 30px;
      height: 30px;
      border-radius: 15px;
      background-color: rgba(255, 45, 85, 0.1);
      border: 1px solid rgba(255, 45, 85, 0.2);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      position: relative;
    }

    .edvinity-minimize:hover,
    .edvinity-close:hover {
      background-color: rgba(255, 45, 85, 0.2);
      transform: scale(1.05);
    }

    .edvinity-minimize:active,
    .edvinity-close:active {
      transform: scale(0.95);
    }

    .edvinity-minimize svg,
    .edvinity-close svg {
      width: 16px;
      height: 16px;
      stroke: ${COLORS.PRIMARY};
      stroke-width: 2;
      transition: transform 0.2s ease;
    }

    .edvinity-close:hover svg {
      transform: rotate(90deg);
    }

    .edvinity-panel.minimized .edvinity-minimize svg {
      transform: rotate(180deg);
    }

    .edvinity-content {
      flex: 1;
      overflow: hidden;
      display: flex;
    }

    /* Scrollbar styles for tab content */
    .edvinity-tab-content::-webkit-scrollbar,
    .edvinity-tabs::-webkit-scrollbar {
      width: 5px;
    }

    .edvinity-tab-content::-webkit-scrollbar-track,
    .edvinity-tabs::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }

    .edvinity-tab-content::-webkit-scrollbar-thumb,
    .edvinity-tabs::-webkit-scrollbar-thumb {
      background-color: ${COLORS.PRIMARY};
      border-radius: 3px;
      transition: background-color 0.3s ease;
    }

    .edvinity-tab-content::-webkit-scrollbar-thumb:hover,
    .edvinity-tabs::-webkit-scrollbar-thumb:hover {
      background-color: ${COLORS.PRIMARY_HOVER};
    }

    /* Panel mode specific styles */
    .edvinity-panel-content {
      overflow-y: auto;
      padding: 0;
      scrollbar-width: thin;
      scrollbar-color: ${COLORS.PRIMARY} transparent;
    }

    .edvinity-panel-content::-webkit-scrollbar {
      width: 5px;
    }

    .edvinity-panel-content::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }

    .edvinity-panel-content::-webkit-scrollbar-thumb {
      background-color: ${COLORS.PRIMARY};
      border-radius: 3px;
      transition: background-color 0.3s ease;
    }

    .edvinity-panel-content::-webkit-scrollbar-thumb:hover {
      background-color: ${COLORS.PRIMARY_HOVER};
    }

    /* Section styles */
    .edvinity-section {
      margin-bottom: 24px;
      position: relative;
      padding: 0 20px;
    }

    .edvinity-section:first-child {
      margin-top: 16px;
    }

    .edvinity-section-header {
      display: flex;
      align-items: center;
      margin-bottom: 18px;
      padding-bottom: 8px;
      position: relative;
    }

    .edvinity-section-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 40px;
      height: 2px;
      background: linear-gradient(to right, ${COLORS.PRIMARY}, transparent);
      border-radius: 1px;
    }

    /* Section icon styles - visible in panel mode, hidden in tabs mode */
    .edvinity-section-icon {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      color: ${COLORS.PRIMARY};
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 45, 85, 0.1);
      border-radius: 8px;
      padding: 5px;
    }

    .edvinity-tabs-layout .edvinity-section-icon {
      display: none;
    }

    .edvinity-section-icon svg {
      width: 20px;
      height: 20px;
      stroke-width: 2;
    }

    .edvinity-section-title {
      font-family: ${FONTS.SYSTEM};
      font-size: ${FONTS.SIZE_MEDIUM};
      font-weight: ${FONTS.WEIGHT_BOLD};
      color: ${COLORS.TEXT_PRIMARY};
      letter-spacing: ${FONTS.LETTER_SPACING};
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Panel mode specific section styles */
    .edvinity-panel-mode .edvinity-section-header {
      margin-bottom: 10px;
      padding-bottom: 6px;
    }

    .edvinity-panel-mode .edvinity-section-icon {
      width: 20px;
      height: 20px;
      padding: 4px;
    }

    .edvinity-panel-mode .edvinity-section-title {
      font-size: 14px;
    }

    .edvinity-items {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .edvinity-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14px;
      background-color: ${COLORS.BG_CARD};
      border-radius: 12px;
      border: 1px solid ${COLORS.BORDER_LIGHT};
      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
      cursor: pointer;
      position: relative;
      overflow: hidden;
      margin-bottom: 10px;
      flex-wrap: wrap;
      box-sizing: border-box;
      width: 100%;
    }

    .edvinity-item::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.03), transparent);
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    .edvinity-item:hover {
      background-color: ${COLORS.BG_CARD_HOVER};
      transform: translateY(-2px);
      box-shadow: ${COLORS.SHADOW_SMALL}, 0 0 0 1px rgba(255, 45, 85, 0.1);
      border-color: rgba(255, 45, 85, 0.15);
    }

    .edvinity-item:hover::after {
      opacity: 1;
    }

    .edvinity-item:active {
      transform: translateY(-1px);
      transition-duration: 0.1s;
    }

    .edvinity-item-content {
      flex: 1;
      min-width: 0; /* Allow content to shrink below its minimum content size */
      margin-right: 10px;
    }

    .edvinity-item-title {
      font-family: ${FONTS.SYSTEM};
      font-size: 14px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      color: ${COLORS.TEXT_PRIMARY};
      margin-bottom: 3px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .edvinity-item-description {
      font-family: ${FONTS.SYSTEM};
      font-size: 12px;
      color: ${COLORS.TEXT_SECONDARY};
      line-height: 1.3;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    /* Panel mode specific item styles */
    .edvinity-panel-mode .edvinity-item {
      padding: 10px 12px;
      margin-bottom: 6px;
      border-radius: 8px;
    }

    .edvinity-panel-mode .edvinity-item-title {
      font-size: 13px;
    }

    .edvinity-panel-mode .edvinity-item-description {
      font-size: 11px;
    }

    /* Completely redesigned toggle switch */
    .edvinity-toggle {
      position: relative;
      width: 50px;
      height: 24px;
      border-radius: 12px;
      background-color: ${COLORS.TOGGLE_BG_OFF};
      transition: background-color 0.3s ease;
      cursor: pointer;
      flex-shrink: 0;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(0, 0, 0, 0.15);
      overflow: hidden;
      margin: 0 2px;
    }

    /* Subtle inner highlight */
    .edvinity-toggle::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom,
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0)
      );
      opacity: 0.5;
      pointer-events: none;
      border-radius: 12px;
    }

    /* Hover effect */
    .edvinity-toggle:hover {
      transform: scale(1.05);
      transition: transform 0.2s ease;
    }

    /* Active state */
    .edvinity-toggle.active {
      background-color: ${COLORS.TOGGLE_BG_ON};
      border-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2);
    }

    /* Toggle handle with hole in center - perfectly centered vertically */
    .edvinity-toggle-handle {
      position: absolute;
      top: 50%;
      left: 2px;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      border-radius: 10px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;
      /* Create a donut shape with a radial gradient */
      background: radial-gradient(
        circle at center,
        transparent 4px,
        #fff 4px,
        #fff 10px
      );
    }

    /* Active handle styles - combined properties */
    .edvinity-toggle.active .edvinity-toggle-handle {
      transform: translate(26px, -50%);
      background: radial-gradient(
        circle at center,
        ${COLORS.PRIMARY} 4px,
        #fff 4px,
        #fff 10px
      );
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 4px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4);
    }

    /* ON/OFF indicators */
    .edvinity-toggle::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.5);
      transition: all 0.3s ease;
      opacity: 0.7;
    }

    .edvinity-toggle.active::after {
      right: auto;
      left: 10px;
      background-color: rgba(255, 255, 255, 0.8);
      box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
    }

    /* Improved toggle animations */
    @keyframes edvinityTogglePulse {
      0% { box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4); }
      50% { box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 4px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2); }
      100% { box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2); }
    }

    @keyframes edvinityToggleHandleSlide {
      0% { transform: translate(0, -50%); }
      100% { transform: translate(26px, -50%); }
    }

    @keyframes edvinityToggleHandleSlideBack {
      0% { transform: translate(26px, -50%); }
      100% { transform: translate(0, -50%); }
    }

    @keyframes edvinityHandleGlow {
      0% {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        background: radial-gradient(circle at center, transparent 4px, #fff 4px, #fff 10px);
      }
      100% {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 4px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4);
        background: radial-gradient(circle at center, ${COLORS.PRIMARY} 4px, #fff 4px, #fff 10px);
      }
    }

    .edvinity-toggle.active {
      animation: edvinityTogglePulse 0.5s ease-out forwards;
    }

    /* Add a subtle animation to make the toggle more interactive */
    @keyframes edvinityTogglePulse {
      0% { box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2); }
      100% { box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2); }
    }

    .edvinity-toggle.active {
      animation: edvinityTogglePulse 0.2s ease-out;
    }



    /* Dropdown select styles */
    .edvinity-select-container {
      position: relative;
      min-width: 120px;
      flex-shrink: 0;
    }

    .edvinity-select-container::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 12px;
      transform: translateY(-50%);
      width: 10px;
      height: 10px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23${COLORS.PRIMARY.replace('#', '')}' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      pointer-events: none;
      z-index: 2;
      transition: transform 0.3s ease;
    }

    .edvinity-select-container:hover::after {
      transform: translateY(-50%) scale(1.2);
    }

    .edvinity-select {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      background-color: ${COLORS.BG_CARD};
      background-image:
        linear-gradient(45deg, transparent 50%, ${COLORS.TEXT_SECONDARY} 50%, ${COLORS.TEXT_SECONDARY} 60%, transparent 60%),
        linear-gradient(-45deg, transparent 50%, ${COLORS.TEXT_SECONDARY} 50%, ${COLORS.TEXT_SECONDARY} 60%, transparent 60%),
        linear-gradient(to bottom, ${COLORS.BG_CARD_HOVER}, ${COLORS.BG_CARD});
      background-position:
        calc(100% - 12px) center,
        calc(100% - 8px) center,
        0 0;
      background-size:
        4px 4px,
        4px 4px,
        100% 100%;
      background-repeat: no-repeat;
      border: 1px solid ${COLORS.BORDER};
      border-radius: 10px;
      padding: 10px 35px 10px 14px;
      font-family: ${FONTS.SYSTEM};
      font-size: 13px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      color: ${COLORS.TEXT_PRIMARY};
      cursor: pointer;
      width: 100%;
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.05);
      text-overflow: ellipsis;
      position: relative;
    }

    .edvinity-select:hover {
      border-color: ${COLORS.PRIMARY};
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      transform: translateY(-1px);
      background-image:
        linear-gradient(45deg, transparent 50%, ${COLORS.PRIMARY} 50%, ${COLORS.PRIMARY} 60%, transparent 60%),
        linear-gradient(-45deg, transparent 50%, ${COLORS.PRIMARY} 50%, ${COLORS.PRIMARY} 60%, transparent 60%),
        linear-gradient(to bottom, ${COLORS.BG_CARD_ACTIVE}, ${COLORS.BG_CARD_HOVER});
    }

    .edvinity-select:focus {
      outline: none;
      border-color: ${COLORS.PRIMARY};
      box-shadow: 0 0 0 3px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3), 0 4px 12px rgba(0, 0, 0, 0.15);
      background-image:
        linear-gradient(45deg, transparent 50%, ${COLORS.PRIMARY} 50%, ${COLORS.PRIMARY} 60%, transparent 60%),
        linear-gradient(-45deg, transparent 50%, ${COLORS.PRIMARY} 50%, ${COLORS.PRIMARY} 60%, transparent 60%),
        linear-gradient(to bottom, ${COLORS.BG_CARD_ACTIVE}, ${COLORS.BG_CARD_HOVER});
    }

    .edvinity-select option {
      background-color: ${COLORS.BG_DARK};
      color: ${COLORS.TEXT_PRIMARY};
      padding: 10px 12px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      border-radius: 4px;
      margin: 2px 0;
    }

    .edvinity-select option:hover {
      background-color: ${COLORS.BG_CARD_HOVER};
    }

    .edvinity-select option:checked {
      background-color: ${COLORS.PRIMARY};
      color: white;
    }

    /* Enhanced text input styles */
    .edvinity-input-container {
      position: relative;
      min-width: 120px;
      flex-shrink: 0;
    }

    .edvinity-input-container::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 12px;
      transform: translateY(-50%);
      width: 16px;
      height: 16px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23${COLORS.PRIMARY.replace('#', '')}' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      pointer-events: none;
      z-index: 2;
      opacity: 0;
      transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .edvinity-input-container:hover::after {
      opacity: 0.5;
    }

    .edvinity-input-container:focus-within::after {
      opacity: 0.8;
      transform: translateY(-50%) scale(1.1);
    }

    .edvinity-input {
      background-color: ${COLORS.BG_CARD};
      background-image: linear-gradient(to bottom, ${COLORS.BG_CARD}, ${COLORS.BG_CARD_HOVER});
      border: 1px solid ${COLORS.BORDER};
      border-radius: 10px;
      padding: 12px 40px 12px 16px;
      font-family: ${FONTS.SYSTEM};
      font-size: 13px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      color: ${COLORS.TEXT_PRIMARY};
      width: 100%;
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.05);
      box-sizing: border-box;
    }

    .edvinity-input:hover {
      border-color: ${COLORS.PRIMARY};
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      transform: translateY(-1px);
      background-image: linear-gradient(to bottom, ${COLORS.BG_CARD_HOVER}, ${COLORS.BG_CARD_ACTIVE});
    }

    .edvinity-input:focus {
      outline: none;
      border-color: ${COLORS.PRIMARY};
      box-shadow: 0 0 0 3px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3), 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
      background-image: linear-gradient(to bottom, ${COLORS.BG_CARD_HOVER}, ${COLORS.BG_CARD_ACTIVE});
    }

    .edvinity-input::placeholder {
      color: ${COLORS.TEXT_MUTED};
      font-style: italic;
      opacity: 0.7;
    }

    .edvinity-input:focus::placeholder {
      opacity: 0.5;
      transform: translateX(2px);
    }

    /* Select dropdown styles */
    .edvinity-select-container {
      position: relative;
      width: 100px;
      margin-left: auto;
      flex-shrink: 0;
    }

    /* Adjust select container when sidebar is expanded */
    .edvinity-tabs.expanded ~ .edvinity-tab-content .edvinity-select-container {
      width: 90px;
    }

    .edvinity-select {
      width: 100%;
      height: 32px;
      padding: 0 24px 0 8px;
      background-color: ${COLORS.BG_CARD};
      border: 1px solid ${COLORS.BORDER};
      border-radius: 6px;
      color: ${COLORS.TEXT_PRIMARY};
      font-family: ${FONTS.SYSTEM};
      font-size: 12px;
      appearance: none;
      -webkit-appearance: none;
      cursor: pointer;
      transition: all 0.2s ease;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    /* Adjust select when sidebar is expanded */
    .edvinity-tabs.expanded ~ .edvinity-tab-content .edvinity-select {
      font-size: 11px;
      padding: 0 20px 0 6px;
    }

    .edvinity-select:focus {
      outline: none;
      border-color: ${COLORS.PRIMARY};
      box-shadow: 0 0 0 2px rgba(255, 45, 85, 0.3);
    }

    .edvinity-select-arrow {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23${COLORS.PRIMARY.replace('#', '')}' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      pointer-events: none;
      z-index: 2;
      transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      opacity: 0.7;
    }

    /* Adjust select arrow when sidebar is expanded */
    .edvinity-tabs.expanded ~ .edvinity-tab-content .edvinity-select-arrow {
      right: 6px;
      width: 7px;
      height: 7px;
    }

    .edvinity-select:hover + .edvinity-select-arrow {
      transform: translateY(-50%) scale(1.2);
      opacity: 1;
    }

    .edvinity-select:focus + .edvinity-select-arrow {
      transform: translateY(-50%) rotate(180deg) scale(1.2);
      opacity: 1;
    }

    /* Remove hover effects for items with inputs */
    .edvinity-item.no-click {
      cursor: default;
    }

    .edvinity-item.no-click:hover {
      transform: none;
      box-shadow: none;
      border-color: ${COLORS.BORDER_LIGHT};
    }

    /* BULLETPROOF dropdown connection - NO GAPS */
    .edvinity-dropdown-content {
      display: none;
      background-color: ${COLORS.BG_CARD};
      border: 1px solid ${COLORS.BORDER_LIGHT};
      border-radius: 12px;
      padding: 14px;
      margin: 0;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      position: relative;
    }

    /* Show dropdown when visible */
    .edvinity-dropdown-content.visible {
      display: block;
      opacity: 1;
      visibility: visible;
      margin-top: -2px !important;
      border-top: none !important;
      border-top-left-radius: 0 !important;
      border-top-right-radius: 0 !important;
      z-index: 999;
    }





    /* Parent item with dropdown */
    .edvinity-item[data-has-dropdown="true"] {
      border-radius: 12px;
      transition: all 0.2s ease;
      position: relative;
    }

    /* When expanded - FORCE flat bottom */
    .edvinity-item[data-has-dropdown="true"][data-expanded="true"] {
      border-bottom-left-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
      border-bottom: none !important;
      margin-bottom: 0 !important;
      z-index: 1000;
    }

    /* Dropdown toggle indicator with smooth animation */
    .edvinity-item[data-has-dropdown="true"] .edvinity-item-title::after {
      content: "";
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-left: 8px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23${COLORS.PRIMARY.replace('#', '')}' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
      vertical-align: middle;
      opacity: 0.7;
    }

    .edvinity-item[data-has-dropdown="true"][data-expanded="true"] .edvinity-item-title::after {
      transform: rotate(180deg);
      opacity: 1;
    }

    /* Hover effect for dropdown items */
    .edvinity-item[data-has-dropdown="true"]:hover .edvinity-item-title::after {
      transform: scale(1.1);
      opacity: 1;
    }

    .edvinity-item[data-has-dropdown="true"][data-expanded="true"]:hover .edvinity-item-title::after {
      transform: scale(1.1) rotate(180deg);
      opacity: 1;
    }

    /* Child items in dropdown - match regular items */
    .edvinity-dropdown-content .edvinity-item {
      margin-bottom: 10px;
    }

    .edvinity-dropdown-content .edvinity-item:last-child {
      margin-bottom: 0;
    }

    .edvinity-child-item:last-child {
      margin-bottom: 0;
    }

    .edvinity-dropdown-item.expanded + .edvinity-children-container {
      max-height: 500px;
      opacity: 1;
    }

    /* Blur effect styles */
    .edvinity-blur-low {
      filter: blur(3px);
      transition: filter 0.3s ease;
    }

    .edvinity-blur-medium {
      filter: blur(6px);
      transition: filter 0.3s ease;
    }

    .edvinity-blur-high {
      filter: blur(10px);
      transition: filter 0.3s ease;
    }

    /* Name spoofing styles - inherit original styling */
    .edvinity-spoofed-name {
      /* No color override - inherit from original element */
      /* No font-weight override - inherit from original element */
      /* This ensures spoofed names match the original styling */
    }

    .edvinity-divider {
      height: 1px;
      background: linear-gradient(to right,
        transparent,
        ${COLORS.PRIMARY}40,
        transparent
      );
      margin: 20px 16px;
      position: relative;
    }

    .edvinity-divider::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 8px;
      height: 8px;
      background-color: ${COLORS.PRIMARY};
      border-radius: 50%;
      box-shadow: 0 0 8px ${COLORS.PRIMARY};
    }

    .edvinity-footer {
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid ${COLORS.BORDER};
      background: linear-gradient(to top, rgba(255, 45, 85, 0.03), transparent);
      position: relative;
      min-height: 50px; /* Ensure minimum height for the footer */
    }

    .edvinity-footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(to right,
        transparent,
        rgba(255, 45, 85, 0.2),
        transparent
      );
    }

    /* UI Mode Selector Styles */
    .edvinity-ui-mode-container {
      display: flex;
      align-items: center;
      flex: 1; /* Allow container to grow */
      max-width: 70%; /* Limit maximum width */
    }

    .edvinity-ui-mode-buttons {
      display: flex;
      border-radius: 10px;
      overflow: hidden;
      border: 1px solid ${COLORS.BORDER};
      margin-right: auto; /* Push to the far left */
      position: relative;
      background-color: ${COLORS.BG_CARD};
      padding: 3px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 1px rgba(255, 255, 255, 0.05);
      max-width: 240px; /* Limit width to prevent overflow */
      flex-shrink: 1; /* Allow shrinking if needed */
    }

    /* Style each button individually */
    .edvinity-ui-mode-button {
      flex: 1;
      padding: 8px 10px;
      margin: 0 2px;
      border-radius: 8px;
      background-color: transparent;
      border: none;
      color: ${COLORS.TEXT_SECONDARY};
      font-family: ${FONTS.SYSTEM};
      font-size: 12px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      cursor: pointer;
      text-align: center;
      letter-spacing: 0.3px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      position: relative;
    }

    .edvinity-ui-mode-button:hover {
      color: ${COLORS.TEXT_PRIMARY};
      transform: translateY(-1px);
    }

    .edvinity-ui-mode-button.active {
      color: white;
      font-weight: ${FONTS.WEIGHT_BOLD};
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      background: linear-gradient(135deg, ${COLORS.PRIMARY}, ${COLORS.PRIMARY_HOVER});
      box-shadow: 0 2px 6px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4);
      transform: none;
    }

    /* Remove hover effect from active button */
    .edvinity-ui-mode-button.active:hover {
      transform: none;
    }

    @keyframes edvinityButtonPulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .edvinity-ui-mode-button.active {
      animation: edvinityButtonPulse 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .edvinity-reset {
      font-family: ${FONTS.SYSTEM};
      font-size: 12px;
      color: ${COLORS.PRIMARY};
      background: rgba(255, 45, 85, 0.1);
      border: 1px solid rgba(255, 45, 85, 0.2);
      border-radius: 6px;
      cursor: pointer;
      padding: 6px 12px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      white-space: nowrap;
      flex-shrink: 0; /* Prevent button from shrinking */
      min-width: 80px; /* Ensure minimum width */
      justify-content: center; /* Center content */
      margin-left: 10px; /* Add space between UI mode selector and reset button */
    }

    .edvinity-reset:hover {
      background: rgba(255, 45, 85, 0.15);
      transform: translateY(-1px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .edvinity-reset:active {
      transform: translateY(0);
      background: rgba(255, 45, 85, 0.2);
    }

    .edvinity-reset::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 4px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF2D55' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8'%3E%3C/path%3E%3Cpath d='M3 3v5h5'%3E%3C/path%3E%3C/svg%3E");
      background-size: contain;
      background-repeat: no-repeat;
    }

    .edvinity-reset:hover {
      background: rgba(255, 45, 85, 0.15);
      transform: translateY(-1px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .edvinity-reset:active {
      transform: translateY(0);
    }

    /* Enhanced animations */
    @keyframes edvinitySlideIn {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }

    @keyframes edvinityFadeIn {
      from { opacity: 0; transform: scale(0.98); }
      to { opacity: 1; transform: scale(1); }
    }

    @keyframes edvinityPop {
      0% { transform: scale(0.95); opacity: 0; }
      70% { transform: scale(1.02); opacity: 1; }
      100% { transform: scale(1); opacity: 1; }
    }

    .edvinity-section {
      animation: edvinitySlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
      opacity: 0;
      transform-origin: center top;
      will-change: transform, opacity;
    }

    /* Tabs mode animation */
    .edvinity-panel.edvinity-tabs-mode .edvinity-section {
      animation: edvinityPop 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    }

    .edvinity-section:nth-child(1) { animation-delay: 0.05s; }
    .edvinity-section:nth-child(2) { animation-delay: 0.1s; }
    .edvinity-section:nth-child(3) { animation-delay: 0.15s; }
    .edvinity-section:nth-child(4) { animation-delay: 0.2s; }
    .edvinity-section:nth-child(5) { animation-delay: 0.25s; }
    .edvinity-section:nth-child(6) { animation-delay: 0.3s; }

    /* Add a subtle hover effect to sections */
    .edvinity-section {
      transition: transform 0.3s cubic-bezier(0.19, 1, 0.22, 1);
    }

    .edvinity-section:hover {
      transform: translateY(-2px);
    }

    .edvinity-item {
      animation: edvinitySlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
      opacity: 0;
      transition: transform 0.3s cubic-bezier(0.19, 1, 0.22, 1),
                  box-shadow 0.3s cubic-bezier(0.19, 1, 0.22, 1);
    }

    .edvinity-panel.edvinity-tabs-mode .edvinity-item {
      animation: edvinityFadeIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    }

    .edvinity-item:nth-child(1) { animation-delay: 0.05s; }
    .edvinity-item:nth-child(2) { animation-delay: 0.1s; }
    .edvinity-item:nth-child(3) { animation-delay: 0.15s; }
    .edvinity-item:nth-child(4) { animation-delay: 0.2s; }
    .edvinity-item:nth-child(5) { animation-delay: 0.25s; }

    /* Enhanced info item styles */
    .edvinity-info-item {
      background-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.08) !important;
      border-left: 3px solid ${COLORS.PRIMARY} !important;
      position: relative;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    }

    .edvinity-info-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.1),
        transparent 70%
      );
      z-index: 0;
    }

    .edvinity-info-item::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 30px;
      height: 30px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23${COLORS.PRIMARY.replace('#', '')}' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpath d='M12 16v-4'%3E%3C/path%3E%3Cpath d='M12 8h.01'%3E%3C/path%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: 16px;
      opacity: 0.3;
      z-index: 0;
    }

    .edvinity-info-item .edvinity-item-content {
      position: relative;
      z-index: 1;
    }

    .edvinity-info-item:hover {
      background-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.12) !important;
      transform: translateX(2px);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
    }

    .edvinity-info-item .edvinity-item-title {
      color: ${COLORS.PRIMARY};
      font-weight: ${FONTS.WEIGHT_BOLD};
      padding-right: 40px; /* Make room for the keyboard icon */
    }

    .edvinity-info-item .edvinity-item-description {
      padding-right: 40px; /* Make room for the keyboard icon */
    }

    /* Enhanced keyboard shortcut styling */
    .edvinity-keyboard-shortcut {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.1);
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2);
      border-radius: 6px;
      padding: 4px 8px;
      font-size: 11px;
      font-weight: ${FONTS.WEIGHT_MEDIUM};
      color: ${COLORS.PRIMARY};
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      opacity: 0.8;
      z-index: 2;
    }

    .edvinity-info-item:hover .edvinity-keyboard-shortcut {
      opacity: 1;
      transform: translateY(-50%) scale(1.05);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
      background: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.15);
    }

    /* Hotkey input styling */
    .edvinity-hotkey-input {
      background-color: ${COLORS.BG_CARD};
      border: 1px solid ${COLORS.BORDER};
      border-radius: 8px;
      padding: 8px 12px;
      font-family: ${FONTS.SYSTEM};
      font-size: 13px;
      color: ${COLORS.TEXT_PRIMARY};
      width: 140px;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      outline: none;
      cursor: pointer;
      user-select: none;
    }

    .edvinity-hotkey-input:hover {
      border-color: ${COLORS.PRIMARY}60;
      background-color: ${COLORS.BG_CARD_HOVER};
    }

    .edvinity-hotkey-input:focus,
    .edvinity-hotkey-input.recording {
      border-color: ${COLORS.PRIMARY};
      background-color: ${COLORS.BG_CARD_HOVER};
      box-shadow: 0 0 0 2px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2);
    }

    .edvinity-hotkey-input.recording {
      animation: edvinityHotkeyPulse 1.5s infinite;
    }

    @keyframes edvinityHotkeyPulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    .edvinity-hotkey-input::placeholder {
      color: ${COLORS.TEXT_MUTED};
      font-style: italic;
    }

    .edvinity-hotkey-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex-shrink: 0;
    }

    /* Accessibility styles */
    .edvinity-no-animations,
    .edvinity-no-animations .edvinity-item,
    .edvinity-no-animations .edvinity-toggle,
    .edvinity-no-animations .edvinity-toggle-handle,
    .edvinity-no-animations .edvinity-section,
    .edvinity-no-animations .edvinity-tab,
    .edvinity-no-animations .edvinity-button {
      animation: none !important;
      transition: none !important;
    }

    .edvinity-reduced-motion * {
      transition-duration: 0.1s !important;
      animation-duration: 0.1s !important;
    }

    .edvinity-high-contrast {
      --contrast-border: 2px solid white;
      --contrast-text: white;
      --contrast-bg: black;
    }

    .edvinity-high-contrast .edvinity-panel,
    .edvinity-high-contrast .edvinity-floating-window {
      border: var(--contrast-border);
      background: var(--contrast-bg);
    }

    .edvinity-high-contrast .edvinity-item {
      border: 1px solid rgba(255, 255, 255, 0.5);
    }

    .edvinity-high-contrast .edvinity-toggle {
      border: var(--contrast-border);
    }

    .edvinity-high-contrast .edvinity-item-title {
      color: var(--contrast-text);
    }

    .edvinity-high-contrast .edvinity-toggle.active {
      background-color: white;
    }

    .edvinity-high-contrast .edvinity-toggle.active .edvinity-toggle-handle {
      background-color: black;
    }

    /* Responsive styles */
    @media (max-width: 480px) {
      .edvinity-panel {
        width: 90%;
        max-width: 340px;
        height: auto;
        max-height: 85vh;
      }

      .edvinity-tabs {
        width: 70px;
      }

      .edvinity-tab {
        padding: 12px 6px;
      }

      .edvinity-tab-icon {
        width: 18px;
        height: 18px;
      }

      .edvinity-tab-title {
        font-size: 10px;
      }

      .edvinity-tab-content {
        padding: 16px;
      }

      .edvinity-item {
        padding: 12px;
      }

      .edvinity-close, .edvinity-minimize {
        width: 26px;
        height: 26px;
      }

      .edvinity-close svg, .edvinity-minimize svg {
        width: 14px;
        height: 14px;
      }
    }
  `;

  // --- Helper Functions ---
  // Optimized createElement function
  const createElement = (tag, {className, id, textContent, innerHTML, attributes} = {}) => {
    const el = document.createElement(tag);
    className && (el.className = className);
    id && (el.id = id);
    textContent && (el.textContent = textContent);
    innerHTML && (el.innerHTML = innerHTML);
    attributes && Object.entries(attributes).forEach(([k, v]) => el.setAttribute(k, v));
    return el;
  };

  // Optimized injectStyles function
  const injectStyles = (css) => {
    const processedCSS = css.replace(/\${COLORS\.([A-Z_]+)}/g, (_, name) => COLORS[name] || _);
    const styleEl = createElement('style', {
      textContent: processedCSS,
      id: 'edvinity-theme-style'
    });
    document.head.appendChild(styleEl);
  };

  // Function to inject theme styles with current colors
  const injectThemeStyles = () => {
    console.log(`🎨 injectThemeStyles called`);
    console.log(`🎨 Current COLORS.PRIMARY: ${COLORS.PRIMARY}`);

    // FIRST: Remove any existing theme style elements
    const existingStyles = document.querySelectorAll('#edvinity-theme-style');
    existingStyles.forEach(style => {
      console.log(`🎨 Removing existing style element`);
      style.remove();
    });

    // Generate CSS with current COLORS values
    const processedCSS = generateCSS();
    console.log(`🎨 Generated CSS length: ${processedCSS.length} characters`);
    console.log(`🎨 CSS sample (first 200 chars):`, processedCSS.substring(0, 200));

    const themeStyle = document.createElement('style');
    themeStyle.id = 'edvinity-theme-style';
    themeStyle.textContent = processedCSS;

    console.log(`🎨 Created style element with ID: ${themeStyle.id}`);

    document.head.appendChild(themeStyle);
    console.log(`🎨 Style element appended to document head`);

    // Verify it was added
    const verifyStyle = document.getElementById('edvinity-theme-style');
    if (verifyStyle) {
      console.log(`🎨 ✅ Style element successfully added to DOM`);
    } else {
      console.log(`🎨 ❌ Style element NOT found in DOM after adding`);
    }
  };

  // Function to generate CSS with current theme colors
  const generateCSS = () => {
    console.log(`🎨 generateCSS called`);
    console.log(`🎨 COLORS object:`, COLORS);

    // Create a function that generates CSS with current colors
    const generatedCSS = `
    /* CSS Variables for easy theming */
    :root {
      --panel-width-tabs: 420px;
      --panel-width-regular: 320px;
      --panel-width: var(--panel-width-tabs);
    }

    /* Custom scrollbar styles */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: ${COLORS.BG_DARK};
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: ${COLORS.BORDER};
      border-radius: 4px;
      border: 2px solid ${COLORS.BG_DARK};
    }

    ::-webkit-scrollbar-thumb:hover {
      background: ${COLORS.PRIMARY}80;
    }

    ::-webkit-scrollbar-corner {
      background: ${COLORS.BG_DARK};
    }

    /* Firefox scrollbar */
    * {
      scrollbar-width: thin;
      scrollbar-color: ${COLORS.BORDER} ${COLORS.BG_DARK};
    }

    /* ONLY target the specific nested dropdown line issue */
    #dropdown-blur-info #dropdown-name-spoofer,
    #setting-name-spoofer + #dropdown-name-spoofer {
      border: none !important;
      box-shadow: none !important;
    }

    /* Toggle switch styles */
    .edvinity-toggle {
      position: relative;
      width: 50px;
      height: 24px;
      border-radius: 12px;
      background-color: ${COLORS.TOGGLE_BG_OFF};
      transition: background-color 0.3s ease;
      cursor: pointer;
      flex-shrink: 0;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(0, 0, 0, 0.15);
      overflow: hidden;
      margin: 0 2px;
    }

    .edvinity-toggle.active {
      background-color: ${COLORS.TOGGLE_BG_ON};
      border-color: rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2);
    }

    .edvinity-toggle-handle {
      position: absolute;
      top: 50%;
      left: 2px;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      border-radius: 10px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;
      background: radial-gradient(
        circle at center,
        transparent 4px,
        #fff 4px,
        #fff 10px
      );
    }

    .edvinity-toggle.active .edvinity-toggle-handle {
      transform: translate(26px, -50%);
      background: radial-gradient(
        circle at center,
        ${COLORS.PRIMARY} 4px,
        #fff 4px,
        #fff 10px
      );
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 4px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4);
    }

    /* Dropdown select styles */
    .edvinity-select {
      background-color: ${COLORS.BG_CARD};
      border: 1px solid ${COLORS.BORDER};
      color: ${COLORS.TEXT_PRIMARY};
      border-radius: 8px;
      padding: 8px 35px 8px 12px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      width: 100%;
      transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      text-overflow: ellipsis;
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
    }

    .edvinity-select:hover {
      border-color: ${COLORS.PRIMARY};
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      transform: translateY(-1px);
    }

    .edvinity-select:focus {
      outline: none;
      border-color: ${COLORS.PRIMARY};
      box-shadow: 0 0 0 2px rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.4);
    }

    .edvinity-select-container::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 12px;
      transform: translateY(-50%);
      width: 10px;
      height: 10px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23${COLORS.PRIMARY.replace('#', '')}' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      pointer-events: none;
      z-index: 2;
      transition: transform 0.3s ease;
    }

    /* Panel styles */
    .edvinity-panel {
      background-color: ${COLORS.BG_DARK};
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.3);
      box-shadow: ${COLORS.SHADOW}, 0 10px 30px rgba(0, 0, 0, 0.4);
    }

    /* Button styles */
    .edvinity-button {
      background: ${COLORS.BG_CARD};
      border: 1px solid rgba(${COLORS.PRIMARY.replace(/^#/, '').match(/../g).map(h => parseInt(h, 16)).join(', ')}, 0.2);
    }

    .edvinity-button.expanded .edvinity-button-icon {
      stroke: ${COLORS.PRIMARY};
    }

    .edvinity-button:not(.expanded):hover .edvinity-button-icon {
      stroke: ${COLORS.PRIMARY};
    }

    /* Add all other critical styles that use theme colors */
    .edvinity-item {
      background-color: ${COLORS.BG_CARD};
      border: 1px solid ${COLORS.BORDER_LIGHT};
      color: ${COLORS.TEXT_PRIMARY};
    }

    .edvinity-item-title {
      color: ${COLORS.TEXT_PRIMARY};
    }

    .edvinity-item-description {
      color: ${COLORS.TEXT_SECONDARY};
    }



    /* Add the rest of the CSS here - this is a simplified version for testing */
    ` + css.replace(/\\\$\{COLORS\.([A-Z_]+)\}/g, (match, name) => {
      const colorValue = COLORS[name];
      console.log(`🎨 Replacing ${match} with ${colorValue}`);
      return colorValue || match;
    });

    console.log(`🎨 Generated CSS sample (toggle styles):`, generatedCSS.substring(generatedCSS.indexOf('.edvinity-toggle'), generatedCSS.indexOf('.edvinity-toggle') + 300));

    return generatedCSS;
  };

  // --- UI Element Creation ---

  const createSettingsButton = () => {
    // Create the main button container
    const buttonContainer = createElement('div', {
      className: 'edvinity-button-container'
    });

    // Create the actual button
    const button = createElement('button', {
      className: 'edvinity-button',
      innerHTML: `
        <span class="edvinity-button-content">
          <svg class="edvinity-button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
          <span class="edvinity-button-text">Settings</span>
        </span>
      `
    });

    // Add the button to the container
    buttonContainer.appendChild(button);

    // Add event listener to handle hover effects
    buttonContainer.addEventListener('mouseenter', () => {
      button.classList.add('expanded');
    });

    buttonContainer.addEventListener('mouseleave', () => {
      button.classList.remove('expanded');
    });

    return buttonContainer;
  };

  const createBackdrop = () => {
    return createElement('div', { className: 'edvinity-backdrop' });
  };

  const createPanel = () => {
    const panel = createElement('div', { className: 'edvinity-panel' });

    // Apply no-animations class if the setting is enabled
    if (window.edvinityNoAnimations) {
      panel.classList.add('edvinity-no-animations');
    }

    // Try to load saved settings for animations
    try {
      const settings = JSON.parse(localStorage.getItem('edvinity-settings') || '{}');
      if (settings.animations === false) {
        panel.classList.add('edvinity-no-animations');
      }
    } catch (e) {
      console.warn('Could not load animation settings:', e);
    }

    return { panel };
  };

  const createHeader = () => {
    const header = createElement('div', { className: 'edvinity-header' });

    // Create drag handle
    const dragHandle = createElement('div', { className: 'edvinity-drag-handle' });

    // Create title container with drag indicator
    const titleContainer = createElement('div', { className: 'edvinity-title-container' });

    const dragIndicator = createElement('div', { className: 'edvinity-drag-indicator' });

    const title = createElement('div', {
      className: 'edvinity-title',
      textContent: 'Edvinity'
    });

    titleContainer.append(dragIndicator, title);

    // Create controls container
    const controlsContainer = createElement('div', { className: 'edvinity-controls' });

    // Create minimize/maximize button
    const minimizeButton = createElement('button', {
      className: 'edvinity-minimize',
      innerHTML: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>`
    });

    // Create close button
    const closeButton = createElement('button', {
      className: 'edvinity-close',
      innerHTML: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>`
    });

    controlsContainer.append(minimizeButton, closeButton);

    header.append(dragHandle, titleContainer, controlsContainer);
    return { header, closeButton, minimizeButton, dragHandle };
  };

  const createContent = () => {
    const content = createElement('div', { className: 'edvinity-content edvinity-scrollbar' });

    // Create tabs layout container
    const tabsLayout = createElement('div', { className: 'edvinity-tabs-layout' });

    // Create tabs navigation
    const tabs = createElement('div', { className: 'edvinity-tabs edvinity-scrollbar' });

    // Create burger menu toggle button - a simple, subtle toggle
    const burgerToggle = createElement('div', {
      className: 'edvinity-burger-toggle',
      innerHTML: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>`
    });

    // Add click event to burger toggle
    burgerToggle.addEventListener('click', (e) => {
      e.stopPropagation();
      tabs.classList.toggle('expanded');

      // Update burger toggle position with animation
      const isExpanded = tabs.classList.contains('expanded');

      // Animate the burger toggle position
      if (isExpanded) {
        burgerToggle.style.transition = 'left 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
        burgerToggle.style.left = '180px';

        // Add a subtle animation to the icon
        const icon = burgerToggle.querySelector('svg');
        if (icon) {
          icon.style.transition = 'transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
          icon.style.transform = 'rotate(180deg)';
        }
      } else {
        burgerToggle.style.transition = 'left 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
        burgerToggle.style.left = '60px';

        // Add a subtle animation to the icon
        const icon = burgerToggle.querySelector('svg');
        if (icon) {
          icon.style.transition = 'transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
          icon.style.transform = 'rotate(0deg)';
        }
      }

      // Save preference to localStorage
      try {
        localStorage.setItem('edvinity-sidebar-expanded', isExpanded);
      } catch (e) {
        console.warn('Could not save sidebar state:', e);
      }
    });

    // We'll append the burger toggle to the tabsLayout instead of tabs
    // This will be done after the tabs are added to the layout

    // Check if sidebar was previously expanded
    try {
      const wasExpanded = localStorage.getItem('edvinity-sidebar-expanded') === 'true';
      if (wasExpanded) {
        tabs.classList.add('expanded');
        // Set initial position of burger toggle for expanded state
        setTimeout(() => {
          burgerToggle.style.left = '180px';

          // Set initial rotation of the icon
          const icon = burgerToggle.querySelector('svg');
          if (icon) {
            icon.style.transform = 'rotate(180deg)';
          }
        }, 0);
      }
    } catch (e) {
      console.warn('Could not load sidebar state:', e);
    }

    // Create tab content container
    const tabContents = {};

    // Create tabs and content areas for each section
    SETTINGS.forEach((section, index) => {
      // Create tab
      const tab = createElement('div', {
        className: `edvinity-tab ${index === 0 ? 'active' : ''}`,
        attributes: {
          'data-tab': section.id
        }
      });

      const tabIcon = createElement('div', {
        className: 'edvinity-tab-icon',
        innerHTML: section.icon
      });

      const tabTitle = createElement('div', {
        className: 'edvinity-tab-title',
        textContent: section.title
      });

      tab.append(tabIcon, tabTitle);
      tabs.appendChild(tab);

      // Create tab content
      const tabContent = createElement('div', {
        className: `edvinity-tab-content ${index === 0 ? 'active' : ''}`,
        id: `tab-content-${section.id}`
      });

      // Create items container
      const items = createElement('div', { className: 'edvinity-items' });

      // Add all setting items
      section.items.forEach(item => {
        const itemEl = createSettingItem(item);
        items.appendChild(itemEl);

        // Handle dropdown creation for toggle-dropdown items
        if (item.type === 'toggle-dropdown' && item.children && Array.isArray(item.children)) {
          console.log('Creating dropdown for item:', item.id, 'with', item.children.length, 'children');

          // Mark this item as having a dropdown
          itemEl.setAttribute('data-has-dropdown', 'true');
          itemEl.setAttribute('data-expanded', 'false');

          // Create dropdown content container
          const dropdownContent = createElement('div', {
            className: 'edvinity-dropdown-content',
            id: `dropdown-${item.id}`
          });

          console.log('Created dropdown container with ID:', `dropdown-${item.id}`);

          // Add child items to dropdown
          item.children.forEach((childItem, index) => {
            console.log(`Creating child item ${index + 1}/${item.children.length}:`, childItem.id, childItem.title);
            const childItemEl = createSettingItem(childItem);
            dropdownContent.appendChild(childItemEl);
            console.log('Child item appended to dropdown:', childItem.id);

            // Handle nested children recursively
            if (childItem.children && Array.isArray(childItem.children) && childItem.type === 'toggle-dropdown') {
              const nestedDropdown = createElement('div', {
                className: 'edvinity-dropdown-content',
                id: `dropdown-${childItem.id}`
              });

              childItem.children.forEach(nestedChild => {
                nestedDropdown.appendChild(createSettingItem(nestedChild));
              });

              dropdownContent.appendChild(nestedDropdown);

              // Add nested dropdown toggle functionality
              childItemEl.setAttribute('data-has-dropdown', 'true');
              childItemEl.setAttribute('data-expanded', 'false');

              childItemEl.addEventListener('click', (e) => {
                const toggle = childItemEl.querySelector('.edvinity-toggle');
                if (e.target === toggle || toggle.contains(e.target)) return;

                const isExpanded = childItemEl.getAttribute('data-expanded') === 'true';
                const newState = !isExpanded;

                childItemEl.setAttribute('data-expanded', newState ? 'true' : 'false');
                nestedDropdown.classList.toggle('visible', newState);
              });
            }
          });

          // Special handling for name-spoofer dropdown - set initial visibility
          if (item.id === 'name-spoofer') {
            setTimeout(() => {
              const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
              const spoofMode = blurSettings['spoof-mode'] || 'preset';
              console.log('Setting initial visibility for name-spoofer dropdown, mode:', spoofMode);
              updateSpoofModeVisibility(spoofMode);
            }, 100);
          }

          // Add dropdown after the item
          items.appendChild(dropdownContent);
          console.log('Dropdown appended to items container for:', item.id);
          console.log('Dropdown children count:', dropdownContent.children.length);

          // Add click handler to toggle dropdown
          itemEl.addEventListener('click', (e) => {
            // Only handle clicks on the item itself, not on the toggle
            const toggle = itemEl.querySelector('.edvinity-toggle');
            if (e.target === toggle || toggle.contains(e.target)) return;

            console.log('Dropdown click detected for:', item.id);

            // Toggle dropdown visibility
            const isExpanded = itemEl.getAttribute('data-expanded') === 'true';
            const newState = !isExpanded;

            console.log('Toggling dropdown from', isExpanded, 'to', newState);

            itemEl.setAttribute('data-expanded', newState ? 'true' : 'false');

            // Ensure dropdown content exists before toggling
            let currentDropdownContent = document.getElementById(`dropdown-${item.id}`);
            if (!currentDropdownContent) {
              console.warn(`Dropdown content missing for ${item.id}, creating it now...`);
              currentDropdownContent = createElement('div', {
                className: 'edvinity-dropdown-content',
                id: `dropdown-${item.id}`
              });

              // Add child items to the newly created dropdown
              if (item.children && Array.isArray(item.children)) {
                item.children.forEach((childItem, index) => {
                  console.log(`Creating missing child item ${index + 1}/${item.children.length}:`, childItem.id, childItem.title);
                  const childItemEl = createSettingItem(childItem);
                  currentDropdownContent.appendChild(childItemEl);
                });
              }

              // Insert the dropdown after the current item
              itemEl.parentNode.insertBefore(currentDropdownContent, itemEl.nextSibling);
              console.log('Missing dropdown created and inserted for:', item.id);
            }

            currentDropdownContent.classList.toggle('visible', newState);

            // If this is the name-spoofer dropdown and it's being opened, update visibility
            if (item.id === 'name-spoofer' && newState) {
              setTimeout(() => {
                const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
                const spoofMode = blurSettings['spoof-mode'] || 'preset';
                console.log('Dropdown opened, updating spoof mode visibility:', spoofMode);
                updateSpoofModeVisibility(spoofMode);
              }, 100);
            }
          });
        }
      });

      tabContent.appendChild(items);
      tabContents[section.id] = tabContent;

      // Add click event to tab
      tab.addEventListener('click', () => {
        // If tab is already active, don't do anything
        if (tab.classList.contains('active')) return;

        // Deactivate all tabs and contents
        const allTabs = document.querySelectorAll('.edvinity-tab');
        const allContents = document.querySelectorAll('.edvinity-tab-content');

        allTabs.forEach(t => t.classList.remove('active'));
        allContents.forEach(c => c.classList.remove('active'));

        // Activate clicked tab without animation
        tab.classList.add('active');

        // Activate the corresponding content with animation
        const contentToActivate = document.getElementById(`tab-content-${section.id}`);
        if (contentToActivate) {
          // Reset animation by removing and re-adding the class
          contentToActivate.style.animation = 'none';
          contentToActivate.offsetHeight; // Force reflow
          contentToActivate.style.animation = '';
          contentToActivate.classList.add('active');
        }
      });
    });

    // Add all tab contents to layout
    Object.values(tabContents).forEach(tabContent => {
      tabsLayout.appendChild(tabContent);
    });

    // Assemble tabs layout
    tabsLayout.insertBefore(tabs, tabsLayout.firstChild);

    // Now add the burger toggle to the tabsLayout
    tabsLayout.appendChild(burgerToggle);

    content.appendChild(tabsLayout);

    return content;
  };

  // Floating content creation function removed as requested
  const createFloatingContent = () => {
    // Return empty content div as a placeholder
    return createElement('div', { className: 'edvinity-content' });
  };

  // Note: floatingWindows is declared later in the code

  // Track window positions in a grid layout - removed as requested
  /*
  const windowPositions = {
    gridSize: { cols: 3, rows: 2 },
    baseOffset: { x: 60, y: 60 },
    spacing: { x: 40, y: 40 },
    windowSize: { width: 360, height: 420 },
    getNextPosition: function() {
      // Count existing windows to determine next position
      const existingWindows = Object.keys(floatingWindows).length;

      // Calculate grid position (row, col)
      const col = existingWindows % this.gridSize.cols;
      const row = Math.floor(existingWindows / this.gridSize.cols) % this.gridSize.rows;

      // Calculate actual pixel position
      const x = this.baseOffset.x + col * (this.windowSize.width + this.spacing.x);
      const y = this.baseOffset.y + row * (this.windowSize.height + this.spacing.y);

      return { x, y };
    }
  };
  */

  // Function to create a floating window for a section - removed as requested
  const createFloatingWindow = (section) => {
    // Stub implementation to prevent errors
    console.log('Floating window creation removed');
    return null;
  };

  /* Commented out floating window code
    // Create window container
    const windowEl = createElement('div', {
      className: 'edvinity-floating-window',
      id: `floating-window-${section.id}`,
      attributes: {
        'data-section': section.id,
        'data-minimized': 'false'
      }
    });
  */

  /* Commented out remaining floating window code */

  // Function to focus a floating window - stub implementation to prevent errors
  const focusFloatingWindow = (windowEl) => {
    // Stub implementation
    console.log('Floating window focus removed');
  };

  // Function to make an element draggable - removed as requested
  /*
  const makeDraggable = (element, handle) => {
    // Implementation removed
  };
  */

  // No longer needed - removed resizable functionality

  const createSection = (section) => {
    const sectionEl = createElement('div', {
      className: 'edvinity-section',
      id: `section-${section.id}`
    });

    // Create section header with icon and title
    const header = createElement('div', { className: 'edvinity-section-header' });

    const icon = createElement('div', {
      className: 'edvinity-section-icon',
      innerHTML: section.icon
    });

    const title = createElement('div', {
      className: 'edvinity-section-title',
      textContent: section.title
    });

    header.append(icon, title);

    // Create items container
    const items = createElement('div', { className: 'edvinity-items' });

    // Process items
    section.items.forEach(item => {
      // Skip child items, they'll be handled with their parents
      if (item.parent) return;

      // Create the item element
      const itemEl = createSettingItem(item);
      items.appendChild(itemEl);

      // Check if this item has child items (either from section.items or item.children)
      let childItems = [];

      // First check if the item has a children property
      if (item.children && Array.isArray(item.children)) {
        childItems = item.children;
      } else {
        // Fallback to the old method for backward compatibility
        childItems = section.items.filter(childItem => childItem.parent === item.id);
      }

      if (childItems.length > 0 && item.type === 'toggle-dropdown') {
        // Mark this item as having a dropdown
        itemEl.setAttribute('data-has-dropdown', 'true');
        itemEl.setAttribute('data-expanded', 'false');

        // Create dropdown content container
        const dropdownContent = createElement('div', {
          className: 'edvinity-dropdown-content',
          id: `dropdown-${item.id}`
        });

        // Add child items to dropdown (recursively handle nested children)
        childItems.forEach(childItem => {
          const childItemEl = createSettingItem(childItem);
          dropdownContent.appendChild(childItemEl);

          // Handle nested children recursively
          if (childItem.children && Array.isArray(childItem.children) && childItem.type === 'toggle-dropdown') {
            const nestedDropdown = createElement('div', {
              className: 'edvinity-dropdown-content',
              id: `dropdown-${childItem.id}`
            });

            childItem.children.forEach(nestedChild => {
              nestedDropdown.appendChild(createSettingItem(nestedChild));
            });

            dropdownContent.appendChild(nestedDropdown);

            // Add nested dropdown toggle functionality
            childItemEl.setAttribute('data-has-dropdown', 'true');
            childItemEl.setAttribute('data-expanded', 'false');

            childItemEl.addEventListener('click', (e) => {
              const toggle = childItemEl.querySelector('.edvinity-toggle');
              if (e.target === toggle || toggle.contains(e.target)) return;

              const isExpanded = childItemEl.getAttribute('data-expanded') === 'true';
              const newState = !isExpanded;

              childItemEl.setAttribute('data-expanded', newState ? 'true' : 'false');
              nestedDropdown.classList.toggle('visible', newState);
            });
          }
        });

        // Add dropdown after the item
        items.appendChild(dropdownContent);

        // Add click handler to toggle dropdown
        itemEl.addEventListener('click', (e) => {
          // Only handle clicks on the item itself, not on the toggle
          const toggle = itemEl.querySelector('.edvinity-toggle');
          if (e.target === toggle || toggle.contains(e.target)) return;

          console.log('Section dropdown click detected for:', item.id);

          // Toggle dropdown visibility
          const isExpanded = itemEl.getAttribute('data-expanded') === 'true';
          const newState = !isExpanded;

          console.log('Section toggling dropdown from', isExpanded, 'to', newState);

          itemEl.setAttribute('data-expanded', newState ? 'true' : 'false');

          // Ensure dropdown content exists before toggling
          let currentDropdownContent = document.getElementById(`dropdown-${item.id}`);
          if (!currentDropdownContent) {
            console.warn(`Section: Dropdown content missing for ${item.id}, creating it now...`);
            currentDropdownContent = createElement('div', {
              className: 'edvinity-dropdown-content',
              id: `dropdown-${item.id}`
            });

            // Add child items to the newly created dropdown
            if (childItems && childItems.length > 0) {
              childItems.forEach((childItem, index) => {
                console.log(`Section: Creating missing child item ${index + 1}/${childItems.length}:`, childItem.id, childItem.title);
                const childItemEl = createSettingItem(childItem);
                currentDropdownContent.appendChild(childItemEl);
              });
            }

            // Insert the dropdown after the current item
            itemEl.parentNode.insertBefore(currentDropdownContent, itemEl.nextSibling);
            console.log('Section: Missing dropdown created and inserted for:', item.id);
          }

          currentDropdownContent.classList.toggle('visible', newState);

          // If this is the name-spoofer dropdown and it's being opened, update visibility
          if (item.id === 'name-spoofer' && newState) {
            setTimeout(() => {
              const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
              const spoofMode = blurSettings['spoof-mode'] || 'preset';
              console.log('Dropdown opened (section), updating spoof mode visibility:', spoofMode);
              updateSpoofModeVisibility(spoofMode);
            }, 100);
          }
        });
      }
    });

    sectionEl.append(header, items);
    return sectionEl;
  };



  const createSettingItem = (item) => {
    // Check if this item has a parent
    const hasParent = item.parent !== undefined;

    const itemEl = createElement('div', {
      className: `edvinity-item ${hasParent ? 'edvinity-child-item' : ''}`,
      id: `setting-${item.id}`,
      attributes: {
        'data-parent': item.parent || '',
        'data-item-id': item.id
      }
    });

    const content = createElement('div', { className: 'edvinity-item-content' });

    const title = createElement('div', {
      className: 'edvinity-item-title',
      textContent: item.title
    });

    const description = createElement('div', {
      className: 'edvinity-item-description',
      textContent: item.description
    });

    content.append(title, description);

    if (item.type === 'toggle' || item.type === 'toggle-dropdown') {
      // Check if we have a saved value for this setting
      let initialValue = item.value;

      // For privacy settings, use the value from privacySettings
      if (item.id === 'blur-info') {
        initialValue = privacySettings['blur-info'];
      } else if (item.id === 'name-spoofer') {
        initialValue = privacySettings['name-spoofer'];
      } else if (item.id === 'blur-profile-pics') {
        initialValue = privacySettings['blur-profile-pics'];
      } else if (item.id === 'blur-student-ids') {
        initialValue = privacySettings['blur-student-ids'];
      }

      // Create a toggle with actual functionality
      const toggle = createElement('div', {
        className: `edvinity-toggle ${initialValue ? 'active' : ''}`,
        attributes: {
          'data-value': initialValue ? 'true' : 'false',
          'data-setting-id': item.id
        }
      });

      // Create the handle (dot is now a pseudo-element)
      const handle = createElement('div', {
        className: 'edvinity-toggle-handle'
      });

      // Add handle to toggle
      toggle.appendChild(handle);

      // Add toggle functionality with animations and actual functionality for specific settings
      toggle.addEventListener('click', (e) => {
        e.stopPropagation();
        // Get current state
        const isActive = toggle.classList.contains('active');

        // Get the handle element
        const handle = toggle.querySelector('.edvinity-toggle-handle');

        // Reset animations and force reflow to ensure animations restart
        toggle.style.animation = '';
        handle.style.animation = '';
        void toggle.offsetWidth;
        void handle.offsetWidth;

        // Toggle the class and update data attribute
        toggle.classList.toggle('active');
        toggle.setAttribute('data-value', !isActive ? 'true' : 'false');

        // Apply appropriate animations based on new state
        if (!isActive) {
          toggle.style.animation = 'edvinityTogglePulse 0.5s ease-out forwards';
          handle.style.animation = 'edvinityToggleHandleSlide 0.2s ease forwards, edvinityHandleGlow 0.3s ease forwards';
        } else {
          handle.style.animation = 'edvinityToggleHandleSlideBack 0.2s ease forwards';
        }

        // Handle dropdown visibility
        const newState = !isActive;

        // For toggle-dropdown items
        if (item.type === 'toggle-dropdown') {
          const parentItem = itemEl.closest('.edvinity-item[data-has-dropdown="true"]') || itemEl;
          const dropdownContent = document.getElementById(`dropdown-${item.id}`);

          if (newState) {
            // Show dropdown
            parentItem?.setAttribute('data-expanded', 'true');
            dropdownContent?.classList.add('visible');
          } else {
            // Hide dropdown
            parentItem?.setAttribute('data-expanded', 'false');
            dropdownContent?.classList.remove('visible');
          }
        }

        // For parent items, show/hide child items
        if (item.parent) {
          document.querySelectorAll(`.edvinity-item[data-parent="${item.id}"]`)
            .forEach(childItem => {
              childItem.style.display = newState ? 'flex' : 'none';
            });
        }

        // Apply actual functionality for specific settings
        const settingId = item.id;

        // Handle blur info toggle
        if (settingId === 'blur-info') {
          // Update privacy settings state
          privacySettings['blur-info'] = newState;

          // Apply blur effect
          toggleBlurInfo(newState);

          // Save privacy settings to localStorage
          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle name spoofer toggle
        if (settingId === 'name-spoofer') {
          // Update privacy settings state
          privacySettings['name-spoofer'] = newState;

          // Apply name spoofing
          const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
          const spoofMode = blurSettings['spoof-mode'] || 'preset';
          const presetName = blurSettings['preset-names'] || 'student';
          const customName = blurSettings['custom-name'] || 'Anonymous';

          applyNameSpoofing(newState, spoofMode, spoofMode === 'custom' ? customName : presetName);

          // Save privacy settings to localStorage
          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle individual blur toggles
        if (settingId === 'blur-profile-pics') {
          console.log('Blur profile pics toggle:', newState);
          privacySettings[settingId] = newState;

          // Apply specific blur for profile pics
          applySpecificBlur('profile-pics', newState);

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        if (settingId === 'blur-student-ids') {
          console.log('Blur student IDs toggle:', newState);
          privacySettings[settingId] = newState;

          // Apply specific blur for student IDs
          applySpecificBlur('student-ids', newState);

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle anti-logout toggle
        if (settingId === 'anti-logout') {
          // Update privacy settings state
          privacySettings['anti-logout'] = newState;

          // Apply anti-logout functionality
          toggleAntiLogout(newState);

          // Save privacy settings to localStorage
          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle auto-mute toggle
        if (settingId === 'auto-mute') {
          // Update privacy settings state
          privacySettings['auto-mute'] = newState;

          // Apply auto-mute functionality
          toggleAutoMute(newState);

          // Save privacy settings to localStorage
          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle auto-vocab toggle
        if (settingId === 'auto-vocab') {
          console.log('Auto-vocab toggle:', newState);
          privacySettings[settingId] = newState;

          // Apply auto-vocab functionality
          toggleAutoVocab(newState);

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle vocab sub-settings
        if (settingId === 'vocab-auto-fill') {
          console.log('Vocab auto-fill toggle:', newState);
          privacySettings[settingId] = newState;
          vocabSettings.autoFill = newState;

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        if (settingId === 'vocab-auto-play') {
          console.log('Vocab auto-play toggle:', newState);
          privacySettings[settingId] = newState;
          vocabSettings.autoPlay = newState;

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        if (settingId === 'vocab-auto-submit') {
          console.log('Vocab auto-submit toggle:', newState);
          privacySettings[settingId] = newState;
          vocabSettings.autoSubmit = newState;

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle show-column toggle
        if (settingId === 'show-column') {
          console.log('Show column toggle:', newState);
          privacySettings[settingId] = newState;

          // Apply show column functionality
          toggleShowColumn(newState);

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save privacy settings:', e);
          }
        }

        // Handle background blur toggle
        if (settingId === 'background-blur') {
          console.log('Background blur toggle:', newState);

          // Apply background blur functionality
          toggleBackgroundBlur(newState);

          // Save to appearance settings
          try {
            const savedSettings = JSON.parse(localStorage.getItem('edvinity-appearance-settings') || '{}');
            savedSettings[settingId] = newState;
            localStorage.setItem('edvinity-appearance-settings', JSON.stringify(savedSettings));
          } catch (e) {
            console.warn('Could not save appearance settings:', e);
          }
        }

        // Handle click to close toggle
        if (settingId === 'click-to-close') {
          console.log('Click to close toggle:', newState);

          // Apply click to close functionality
          toggleClickToClose(newState);

          // Save to appearance settings
          try {
            const savedSettings = JSON.parse(localStorage.getItem('edvinity-appearance-settings') || '{}');
            savedSettings[settingId] = newState;
            localStorage.setItem('edvinity-appearance-settings', JSON.stringify(savedSettings));
          } catch (e) {
            console.warn('Could not save appearance settings:', e);
          }
        }




      });

      itemEl.append(content, toggle);

      // Make the whole item clickable
      itemEl.addEventListener('click', (e) => {
        if (e.target !== toggle && !toggle.contains(e.target)) {
          // For dropdown items, clicking anywhere on the item should toggle it
          if (item.type === 'toggle-dropdown') {
            // If toggle is already active, just toggle the dropdown
            if (toggle.classList.contains('active')) {
              const isExpanded = itemEl.classList.contains('expanded');
              if (isExpanded) {
                // Collapse the dropdown
                toggleChildItems(item.id, false);
              } else {
                // Expand the dropdown
                toggleChildItems(item.id, true);
              }
              return;
            }
          }

          // Otherwise, click the toggle
          toggle.click();
        }
      });
    } else if (item.type === 'dropdown' || item.type === 'select') {
      const selectContainer = createElement('div', {
        className: 'edvinity-select-container'
      });

      const select = createElement('select', {
        className: 'edvinity-select',
        attributes: {
          'data-setting-id': item.id
        }
      });

      // Load saved value for this setting
      let currentValue = item.value;
      try {
        const savedSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
        if (savedSettings[item.id] !== undefined) {
          currentValue = savedSettings[item.id];
        }
      } catch (e) {
        console.warn('Could not load saved value for', item.id, e);
      }

      // Add options to select
      if (item.options && Array.isArray(item.options)) {
        item.options.forEach(option => {
          // Handle both string options and object options with value/label
          const optionValue = typeof option === 'object' ? option.value : option;
          const optionLabel = typeof option === 'object' ? option.label : option.charAt(0).toUpperCase() + option.slice(1);

          const optionEl = createElement('option', {
            value: optionValue,
            textContent: optionLabel,
            attributes: {
              selected: optionValue === currentValue ? 'selected' : null
            }
          });
          select.appendChild(optionEl);
        });
      }

      // Add change event listener with functionality for blur settings
      select.addEventListener('change', (e) => {
        const settingId = item.id;
        const newValue = e.target.value;

        // Handle blur intensity changes
        if (settingId === 'blur-intensity') {
          // Sanitize and update the global blur intensity
          const sanitizedValue = sanitizeBlurIntensity(newValue);
          BLUR_INTENSITY = sanitizedValue;
          window.BLUR_INTENSITY = sanitizedValue; // Also set on window for global access

          // Re-apply blur if it's currently enabled
          if (privacySettings['blur-info']) {
            toggleBlurInfo(true);
          }

          // Save the sanitized setting
          try {
            const savedSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
            savedSettings[settingId] = sanitizedValue;
            localStorage.setItem('edvinity-blur-settings', JSON.stringify(savedSettings));
          } catch (e) {
            console.warn('Could not save blur setting:', e);
          }
        }

        // Handle spoof mode changes
        if (settingId === 'spoof-mode') {
          try {
            const savedSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
            savedSettings[settingId] = newValue;
            localStorage.setItem('edvinity-blur-settings', JSON.stringify(savedSettings));

            // Update visibility of preset names and custom name fields
            console.log('Spoof mode changed to:', newValue, 'calling updateSpoofModeVisibility');
            updateSpoofModeVisibility(newValue);

            // Re-apply name spoofing if it's enabled
            if (privacySettings['name-spoofer']) {
              const presetName = savedSettings['preset-names'] || 'student';
              const customName = savedSettings['custom-name'] || 'Anonymous';
              applyNameSpoofing(true, newValue, newValue === 'custom' ? customName : presetName);
            }
          } catch (e) {
            console.warn('Could not save spoof mode setting:', e);
          }
        }

        // Handle preset name changes
        if (settingId === 'preset-names') {
          try {
            const savedSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
            savedSettings[settingId] = newValue;
            localStorage.setItem('edvinity-blur-settings', JSON.stringify(savedSettings));

            // Re-apply name spoofing if it's enabled and using preset mode
            if (privacySettings['name-spoofer']) {
              const spoofMode = savedSettings['spoof-mode'] || 'preset';
              if (spoofMode === 'preset') {
                applyNameSpoofing(true, 'preset', newValue);
              }
            }
          } catch (e) {
            console.warn('Could not save preset name setting:', e);
          }
        }

        // Handle vocab delay changes
        if (settingId === 'vocab-delay') {
          console.log('Vocab delay changed:', newValue);
          privacySettings[settingId] = newValue;
          vocabSettings.delay = parseInt(newValue) || 1000;

          try {
            localStorage.setItem('edvinity-privacy-settings', JSON.stringify(privacySettings));
          } catch (e) {
            console.warn('Could not save vocab delay setting:', e);
          }
        }
      });

      selectContainer.appendChild(select);
      itemEl.append(content, selectContainer);

      // Remove clickable behavior for dropdown items
      itemEl.classList.add('no-click');

    } else if (item.type === 'text' || item.type === 'input') {
      const inputContainer = createElement('div', {
        className: 'edvinity-input-container'
      });

      // Load saved value for this input
      let currentValue = item.value || '';
      try {
        const savedSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
        if (savedSettings[item.id] !== undefined) {
          currentValue = savedSettings[item.id];
        }
      } catch (e) {
        console.warn('Could not load saved value for input', item.id, e);
      }

      const input = createElement('input', {
        className: 'edvinity-input',
        type: 'text',
        value: currentValue,
        attributes: {
          'data-setting-id': item.id,
          placeholder: item.placeholder || 'Enter text...'
        }
      });

      // Add input event listener with functionality for name spoofer
      input.addEventListener('change', (e) => {
        const settingId = item.id;
        const newValue = e.target.value;

        // Handle custom name changes
        if (settingId === 'custom-name') {
          // Save the custom name setting
          try {
            const savedSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
            savedSettings[settingId] = newValue;
            localStorage.setItem('edvinity-blur-settings', JSON.stringify(savedSettings));

            // If name spoofer is enabled and using custom mode, re-apply
            const nameSpoofEnabled = privacySettings['name-spoofer'];
            const spoofMode = savedSettings['spoof-mode'] || 'preset';

            if (nameSpoofEnabled && spoofMode === 'custom') {
              // Re-apply name spoofing with new custom name
              applyNameSpoofing(true, 'custom', newValue);
            }
          } catch (e) {
            console.warn('Could not save custom name setting:', e);
          }
        }
      });

      inputContainer.appendChild(input);
      itemEl.append(content, inputContainer);

      // Remove clickable behavior for text input items
      itemEl.classList.add('no-click');
    } else if (item.type === 'hotkey') {
      // For hotkey items, create a hotkey input field
      const hotkeyContainer = createElement('div', {
        className: 'edvinity-hotkey-container'
      });

      const hotkeyInput = createElement('input', {
        type: 'text',
        className: 'edvinity-hotkey-input',
        value: getHotkey(item.id) || item.value || '',
        placeholder: 'Click to record hotkey',
        readonly: true
      });

      // Set up hotkey recording
      let isRecording = false;
      let recordedKeys = [];

      const startRecording = () => {
        if (isRecording) return;
        isRecording = true;
        recordedKeys = [];
        hotkeyInput.classList.add('recording');
        hotkeyInput.value = 'Press keys...';
        hotkeyInput.focus();
      };

      const stopRecording = () => {
        if (!isRecording) return;
        isRecording = false;
        hotkeyInput.classList.remove('recording');

        if (recordedKeys.length > 0) {
          const hotkeyString = recordedKeys.join('+');
          hotkeyInput.value = hotkeyString;

          // Save the hotkey
          saveHotkey(item.id, hotkeyString);
        } else {
          hotkeyInput.value = item.value || '';
        }
      };

      const handleKeyDown = (e) => {
        if (!isRecording) return;

        e.preventDefault();
        e.stopPropagation();

        recordedKeys = [];

        if (e.ctrlKey) recordedKeys.push('Ctrl');
        if (e.altKey) recordedKeys.push('Alt');
        if (e.shiftKey) recordedKeys.push('Shift');
        if (e.metaKey) recordedKeys.push('Meta');

        // Add the main key if it's not a modifier
        if (!['Control', 'Alt', 'Shift', 'Meta'].includes(e.key)) {
          recordedKeys.push(e.key);
          stopRecording();
        }
      };

      hotkeyInput.addEventListener('click', startRecording);
      hotkeyInput.addEventListener('keydown', handleKeyDown);
      hotkeyInput.addEventListener('blur', stopRecording);

      hotkeyContainer.appendChild(hotkeyInput);
      itemEl.append(content, hotkeyContainer);
      itemEl.classList.add('no-click');
    } else if (item.type === 'info') {
      // For info items, just show the content with a special style
      itemEl.classList.add('edvinity-info-item');
      itemEl.appendChild(content);
    } else {
      itemEl.appendChild(content);
    }

    // Add indentation for child items
    if (hasParent) {
      itemEl.style.paddingLeft = '25px';
      itemEl.style.borderLeft = `2px solid ${COLORS.PRIMARY}`;
      itemEl.style.marginLeft = '10px';
    }

    return itemEl;
  };



  const createDivider = () => {
    return createElement('div', { className: 'edvinity-divider' });
  };

  // Function to toggle child items visibility
  const toggleChildItems = (itemId, show) => {
    const parentItem = document.querySelector(`[data-item-id="${itemId}"]`);
    const dropdownContent = document.getElementById(`dropdown-${itemId}`);

    if (parentItem && dropdownContent) {
      parentItem.setAttribute('data-expanded', show ? 'true' : 'false');
      dropdownContent.classList.toggle('visible', show);

      if (show) {
        parentItem.classList.add('expanded');
      } else {
        parentItem.classList.remove('expanded');
      }
    }
  };

  // UI mode options
  const UI_MODES = {
    TABS: 'tabs',
    PANEL: 'panel'
    // FLOATING: 'floating' - removed as requested
  };

  // Current UI mode - default to tabs mode
  let currentUIMode = UI_MODES.TABS;

  // Track floating windows
  const floatingWindows = {};

  const createFooter = () => {
    const footer = createElement('div', { className: 'edvinity-footer' });

    // Create UI mode selector with buttons instead of dropdown
    const uiModeContainer = createElement('div', {
      className: 'edvinity-ui-mode-container'
    });

    // Create buttons container
    const uiModeButtons = createElement('div', {
      className: 'edvinity-ui-mode-buttons'
    });

    // Create tabs button
    const tabsButton = createElement('button', {
      className: `edvinity-ui-mode-button ${currentUIMode === UI_MODES.TABS ? 'active' : ''}`,
      textContent: 'Tabs',
      attributes: {
        'data-mode': UI_MODES.TABS
      }
    });

    // Create panel button
    const panelButton = createElement('button', {
      className: `edvinity-ui-mode-button ${currentUIMode === UI_MODES.PANEL ? 'active' : ''}`,
      textContent: 'Regular',
      attributes: {
        'data-mode': UI_MODES.PANEL
      }
    });

    // Create floating button - removed as requested
    // const floatingButton = createElement('button', {
    //   className: `edvinity-ui-mode-button ${currentUIMode === UI_MODES.FLOATING ? 'active' : ''}`,
    //   textContent: 'Floating',
    //   attributes: {
    //     'data-mode': UI_MODES.FLOATING
    //   }
    // });

    // Simplified click handlers
    tabsButton.addEventListener('click', () => {
      switchUIMode(UI_MODES.TABS);

      // Update button active states
      tabsButton.classList.add('active');
      panelButton.classList.remove('active');
    });

    panelButton.addEventListener('click', () => {
      switchUIMode(UI_MODES.PANEL);

      // Update button active states
      panelButton.classList.add('active');
      tabsButton.classList.remove('active');
    });

    // Function to switch UI mode
    const switchUIMode = (newMode) => {
      if (newMode !== currentUIMode) {
        const previousMode = currentUIMode;
        currentUIMode = newMode;

        // Save preference
        try {
          localStorage.setItem('edvinity-ui-mode', newMode);
        } catch (e) {
          console.warn('Could not save UI mode preference:', e);
        }

        // Store current panel position before switching
        const rect = panelElement.getBoundingClientRect();
        const currentPosition = {
          left: rect.left,
          top: rect.top,
          isCustomPosition: panelElement.style.marginLeft === '0px' // Check if panel has been moved
        };

        // Floating mode removed as requested

        // Apply the new UI mode immediately
        const existingContent = panelElement.querySelector('.edvinity-content');
        if (existingContent) {
          existingContent.remove();

          // Create new content based on selected UI mode
          let newContent;

          // Remove all mode classes
          panelElement.classList.remove('edvinity-tabs-mode', 'edvinity-panel-mode', 'edvinity-floating-mode');

          if (currentUIMode === UI_MODES.TABS) {
            // Tabs mode
            newContent = createContent();
            panelElement.classList.add('edvinity-tabs-mode');

            // Floating mode implementation removed as requested
          } else if (currentUIMode === UI_MODES.PANEL) {
            // Panel mode
            newContent = createElement('div', { className: 'edvinity-content edvinity-panel-content' });
            SETTINGS.forEach(section => {
              const sectionEl = createSection(section);
              newContent.appendChild(sectionEl);
            });
            panelElement.classList.add('edvinity-panel-mode');

            // Adjust panel size with animation - make it significantly narrower for panel mode
            requestAnimationFrame(() => {
              // Force the width to be much skinnier for panel mode
              document.documentElement.style.setProperty('--panel-width', 'var(--panel-width-regular)');

              // Only reset position if panel hasn't been moved
              if (!currentPosition.isCustomPosition) {
                panelElement.style.marginLeft = 'calc(var(--panel-width-regular) / -2)';
              }

              panelElement.style.transform = 'scale(1.02)';

              // Add a subtle bounce effect
              setTimeout(() => {
                panelElement.style.transform = 'scale(1)';

                // Restore custom position if panel was moved
                if (currentPosition.isCustomPosition) {
                  panelElement.style.left = `${currentPosition.left}px`;
                  panelElement.style.top = `${currentPosition.top}px`;
                }
              }, 300);
            });
          // Floating mode removed as requested

            // Adjust panel size with animation - make it wider for tabs mode
            requestAnimationFrame(() => {
              // Force the width to be wider for tabs mode
              document.documentElement.style.setProperty('--panel-width', 'var(--panel-width-tabs)');

              // Only reset position if panel hasn't been moved
              if (!currentPosition.isCustomPosition) {
                panelElement.style.marginLeft = 'calc(var(--panel-width-tabs) / -2)';
                panelElement.style.marginTop = '-280px';
              }

              panelElement.style.height = '560px';
              panelElement.style.transform = 'scale(1.02)';

              // Add a subtle bounce effect
              setTimeout(() => {
                panelElement.style.transform = 'scale(1)';

                // Restore custom position if panel was moved
                if (currentPosition.isCustomPosition) {
                  panelElement.style.left = `${currentPosition.left}px`;
                  panelElement.style.top = `${currentPosition.top}px`;
                }
              }, 300);
            });
          }

          // Insert new content before footer
          const footer = panelElement.querySelector('.edvinity-footer');
          panelElement.insertBefore(newContent, footer);
        }
      }
    };

    // Set initial active state for the buttons container
    if (currentUIMode === UI_MODES.PANEL) {
      uiModeButtons.classList.add('panel-active');
    }
    // Floating mode removed as requested

    // Assemble UI mode selector
    uiModeButtons.append(tabsButton, panelButton);
    uiModeContainer.append(uiModeButtons);

    const reset = createElement('button', {
      className: 'edvinity-reset',
      textContent: 'Reset All'
    });

    footer.append(uiModeContainer, reset);
    return footer;
  };

  // --- Main Logic ---

  let isOpen = false;
  let isMinimized = false;
  let backdropElement, panelElement;
  let panelPosition = { x: 0, y: 0 };
  let panelState = {
    left: null,
    top: null
  };

  // Hotkey management system
  let customHotkeys = {};

  // Load saved hotkeys from localStorage
  const loadHotkeys = () => {
    try {
      const saved = localStorage.getItem('edvinity-hotkeys');
      if (saved) {
        customHotkeys = JSON.parse(saved);
      }
    } catch (e) {
      console.warn('Failed to load hotkeys:', e);
    }
  };

  // Save hotkey to localStorage
  const saveHotkey = (hotkeyId, hotkeyString) => {
    customHotkeys[hotkeyId] = hotkeyString;
    try {
      localStorage.setItem('edvinity-hotkeys', JSON.stringify(customHotkeys));
    } catch (e) {
      console.warn('Failed to save hotkey:', e);
    }
  };

  // Get hotkey string for a given ID
  const getHotkey = (hotkeyId) => {
    return customHotkeys[hotkeyId] || getDefaultHotkey(hotkeyId);
  };

  // Get default hotkey for a given ID
  const getDefaultHotkey = (hotkeyId) => {
    const defaults = {
      'toggle-panel-hotkey': 'Escape',
      'tabs-mode-hotkey': 'Ctrl+1',
      'regular-mode-hotkey': 'Ctrl+2',
      'next-tab-hotkey': 'Alt+ArrowRight',
      'prev-tab-hotkey': 'Alt+ArrowLeft'
    };
    return defaults[hotkeyId] || '';
  };

  // Parse hotkey string into components
  const parseHotkey = (hotkeyString) => {
    const parts = hotkeyString.split('+');
    const result = {
      ctrlKey: false,
      altKey: false,
      shiftKey: false,
      metaKey: false,
      key: ''
    };

    parts.forEach(part => {
      switch (part) {
        case 'Ctrl':
          result.ctrlKey = true;
          break;
        case 'Alt':
          result.altKey = true;
          break;
        case 'Shift':
          result.shiftKey = true;
          break;
        case 'Meta':
          result.metaKey = true;
          break;
        default:
          result.key = part;
      }
    });

    return result;
  };

  // Check if a keyboard event matches a hotkey
  const matchesHotkey = (event, hotkeyString) => {
    const hotkey = parseHotkey(hotkeyString);
    return event.key === hotkey.key &&
           event.ctrlKey === hotkey.ctrlKey &&
           event.altKey === hotkey.altKey &&
           event.shiftKey === hotkey.shiftKey &&
           event.metaKey === hotkey.metaKey;
  };

  // Initialize hotkeys
  loadHotkeys();

  const openSettings = () => {
    if (isOpen) return;
    isOpen = true;

    // Ensure panel is in maximized state
    if (isMinimized) {
      panelElement.classList.remove('minimized');
      isMinimized = false;
    }

    // First show the backdrop
    backdropElement.classList.add('visible');

    // Then show the panel with a slight delay for a nice sequence
    setTimeout(() => {
      panelElement.classList.add('visible');

      // Reset to default centered position
      panelElement.style.right = 'auto';
      panelElement.style.bottom = 'auto';
      panelElement.style.left = '50%';
      panelElement.style.top = '50%';
      panelElement.style.marginLeft = '-220px'; // Half of 440px width
      panelElement.style.marginTop = '-290px'; // Half of 580px height

      // Reset minimize button icon
      const minimizeButton = panelElement.querySelector('.edvinity-minimize');
      if (minimizeButton) {
        minimizeButton.innerHTML = `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>`;
      }

      // Add a subtle entrance animation
      panelElement.style.animation = 'edvinityPanelEnter 0.4s cubic-bezier(0.19, 1, 0.22, 1) forwards';

      // Remove the animation after it completes
      setTimeout(() => {
        panelElement.style.animation = '';
      }, 400);
    }, 100);
  };

  // Helper function to hide just the main panel without closing floating windows
  const hideMainPanel = () => {
    if (!isOpen) return;

    // Prevent multiple close calls
    if (panelElement.classList.contains('closing')) return;

    isOpen = false;
    panelElement.classList.add('closing');

    // First add a subtle exit animation to the panel
    panelElement.style.animation = 'edvinityPanelExit 0.3s cubic-bezier(0.19, 1, 0.22, 1) forwards';

    // Then hide the panel and backdrop with a slight delay
    setTimeout(() => {
      panelElement.classList.remove('visible');
      panelElement.classList.remove('closing');
      backdropElement.classList.remove('visible');

      // Force end any ongoing drag operations
      const header = panelElement.querySelector('.edvinity-header');
      if (header) {
        header.classList.remove('dragging');
      }
      panelElement.classList.remove('dragging');

      // Reset animation
      setTimeout(() => {
        panelElement.style.animation = '';
      }, 300);
    }, 250);
  };

  const closeSettings = () => {
    if (!isOpen) return;

    // Prevent multiple close calls
    if (panelElement.classList.contains('closing')) return;

    // Close all floating windows only if in floating mode
    if (panelElement.classList.contains('edvinity-floating-mode')) {
      closeAllFloatingWindows();
    }

    // Hide the main panel
    hideMainPanel();
  };

  // Function to close all floating windows
  const closeAllFloatingWindows = () => {
    // Get all open floating windows
    const openWindows = document.querySelectorAll('.edvinity-floating-window:not(.closing)');

    // Close each window with a staggered delay
    openWindows.forEach((window, index) => {
      setTimeout(() => {
        if (window.parentNode) {
          window.classList.add('closing');

          // Remove after animation completes
          setTimeout(() => {
            if (window.parentNode) {
              window.remove();
            }

            // Find and update the corresponding launcher item
            const sectionId = window.getAttribute('data-section');
            if (sectionId) {
              const launcherItem = document.querySelector(`.edvinity-floating-launcher-item[data-section="${sectionId}"]`);
              if (launcherItem) {
                launcherItem.classList.remove('active');
              }
              delete floatingWindows[sectionId];
            }
          }, 300);
        }
      }, index * 50); // Stagger the closing animations
    });
  };

  const setupDraggable = (dragHandle, panel, header) => {
    let isDragging = false;
    let offsetX, offsetY;

    const onMouseDown = (e) => {
      // Don't allow dragging if panel is not visible
      if (!panel.classList.contains('visible')) {
        return;
      }

      // Ignore if clicking on buttons
      if (e.target.closest('.edvinity-minimize') || e.target.closest('.edvinity-close')) {
        return;
      }

      // Only allow dragging from the header area
      isDragging = true;
      panel.classList.add('dragging');
      header.classList.add('dragging');

      // Calculate offset from mouse position to panel's top-left corner
      const rect = panel.getBoundingClientRect();
      offsetX = e.clientX - rect.left;
      offsetY = e.clientY - rect.top;

      // Prevent text selection during drag
      e.preventDefault();
    };

    const onMouseMove = (e) => {
      // Don't do anything if not dragging or panel is not visible
      if (!isDragging || !panel.classList.contains('visible')) return;

      // Calculate new position based on mouse position minus the offset
      const newLeft = e.clientX - offsetX;
      const newTop = e.clientY - offsetY;

      // Keep panel within viewport bounds
      const maxX = window.innerWidth - panel.offsetWidth;
      const maxY = window.innerHeight - panel.offsetHeight;

      const boundedLeft = Math.max(0, Math.min(newLeft, maxX));
      const boundedTop = Math.max(0, Math.min(newTop, maxY));

      // Apply new position using left/top for precise positioning
      panel.style.left = `${boundedLeft}px`;
      panel.style.top = `${boundedTop}px`;
      panel.style.transform = 'none';
      panel.style.marginLeft = '0';
      panel.style.marginTop = '0';
    };

    const onMouseUp = () => {
      // Don't do anything if panel is not visible
      if (!isDragging || !panel.classList.contains('visible')) return;
      isDragging = false;
      panel.classList.remove('dragging');
      header.classList.remove('dragging');
    };

    // Add event listeners
    header.addEventListener('mousedown', onMouseDown);
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);

    // Touch support
    header.addEventListener('touchstart', (e) => {
      // Don't allow dragging if panel is not visible
      if (!panel.classList.contains('visible')) {
        return;
      }

      // Ignore if touching buttons
      if (e.target.closest('.edvinity-minimize') || e.target.closest('.edvinity-close')) {
        return;
      }

      const touch = e.touches[0];
      onMouseDown({
        clientX: touch.clientX,
        clientY: touch.clientY,
        preventDefault: () => e.preventDefault(),
        target: e.target
      });
    }, { passive: false });

    document.addEventListener('touchmove', (e) => {
      // Don't do anything if not dragging or panel is not visible
      if (!isDragging || !panel.classList.contains('visible')) return;
      const touch = e.touches[0];
      onMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
    }, { passive: true });

    document.addEventListener('touchend', onMouseUp);

    // Reset position when window is resized
    window.addEventListener('resize', () => {
      if (isOpen && !isMinimized) {
        // Reset to centered position
        const centerX = (window.innerWidth - panel.offsetWidth) / 2;
        const centerY = (window.innerHeight - panel.offsetHeight) / 2;

        panel.style.left = `${centerX}px`;
        panel.style.top = `${centerY}px`;
        panel.style.transform = 'none';
        panel.style.marginLeft = '0';
        panel.style.marginTop = '0';
      }
    });
  };

  // Removed resizable functionality for simplicity

  // Handle keyboard shortcuts with debouncing
  let keyboardDebounceTimer = null;
  const handleKeyboardShortcuts = (e) => {
    // Don't process shortcuts when typing in input fields or when recording hotkeys
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
      return;
    }

    // Debounce to prevent rapid firing
    if (keyboardDebounceTimer) {
      clearTimeout(keyboardDebounceTimer);
    }

    keyboardDebounceTimer = setTimeout(() => {
      // Toggle panel
      const toggleHotkey = getHotkey('toggle-panel-hotkey');
      if (matchesHotkey(e, toggleHotkey)) {
        if (isOpen) {
          hideMainPanel();
        } else {
          openSettings();
        }
        e.preventDefault();
        return;
      }

      // Only process other shortcuts when panel is open
      if (!isOpen) return;

      // Switch to tabs mode
      const tabsModeHotkey = getHotkey('tabs-mode-hotkey');
      if (matchesHotkey(e, tabsModeHotkey) && currentUIMode !== UI_MODES.TABS) {
        document.querySelector('.edvinity-ui-mode-button[data-mode="tabs"]')?.click();
        e.preventDefault();
        return;
      }

      // Switch to regular mode
      const regularModeHotkey = getHotkey('regular-mode-hotkey');
      if (matchesHotkey(e, regularModeHotkey) && currentUIMode !== UI_MODES.PANEL) {
        document.querySelector('.edvinity-ui-mode-button[data-mode="panel"]')?.click();
        e.preventDefault();
        return;
      }

      // Navigate tabs (only in tabs mode)
      if (currentUIMode === UI_MODES.TABS) {
        const tabs = Array.from(document.querySelectorAll('.edvinity-tab'));
        const activeTabIndex = tabs.findIndex(tab => tab.classList.contains('active'));

        const nextTabHotkey = getHotkey('next-tab-hotkey');
        const prevTabHotkey = getHotkey('prev-tab-hotkey');

        if (matchesHotkey(e, nextTabHotkey) && activeTabIndex < tabs.length - 1) {
          tabs[activeTabIndex + 1].click();
          e.preventDefault();
        } else if (matchesHotkey(e, prevTabHotkey) && activeTabIndex > 0) {
          tabs[activeTabIndex - 1].click();
          e.preventDefault();
        }
      }
    }, 50); // 50ms debounce
  };

  const setupUI = () => {
    injectThemeStyles();

    // Load privacy settings before creating UI elements
    try {
      const savedSettings = JSON.parse(localStorage.getItem('edvinity-privacy-settings') || '{}');

      // Update privacy settings with saved values
      Object.keys(savedSettings).forEach(key => {
        if (key in privacySettings) {
          privacySettings[key] = savedSettings[key];
        }
      });

      // Apply functionality based on saved settings
      if (privacySettings['blur-info']) {
        console.log('Setup: Initializing blur with saved settings:', privacySettings);
        toggleBlurInfo(true);
      }

      if (privacySettings['anti-logout']) {
        toggleAntiLogout(true);
      }

      if (privacySettings['auto-mute']) {
        toggleAutoMute(true);
      }

      if (privacySettings['auto-vocab']) {
        console.log('Setup: Initializing auto vocab with saved settings:', privacySettings);
        // Update vocab settings from saved values
        vocabSettings.autoFill = privacySettings['vocab-auto-fill'] !== false;
        vocabSettings.autoPlay = privacySettings['vocab-auto-play'] !== false;
        vocabSettings.autoSubmit = privacySettings['vocab-auto-submit'] !== false;
        vocabSettings.delay = parseInt(privacySettings['vocab-delay']) || 1000;
        toggleAutoVocab(true);
      }

      if (privacySettings['show-column']) {
        console.log('Setup: Initializing show column with saved settings');
        toggleShowColumn(true);
      }

      // We'll update the toggle UI states after the panel is created
      // This is handled by the MutationObserver in initPrivacyShield
    } catch (e) {
      console.warn('Could not load privacy settings:', e);
    }

    // Load appearance settings
    try {
      const savedAppearanceSettings = JSON.parse(localStorage.getItem('edvinity-appearance-settings') || '{}');

      // Apply background blur setting (default to true if not set)
      const backgroundBlurEnabled = savedAppearanceSettings['background-blur'] !== false;

      // Apply click-to-close setting (default to true if not set)
      const clickToCloseEnabled = savedAppearanceSettings['click-to-close'] !== false;

      // Update the setting values in the SETTINGS array
      const appearanceSection = SETTINGS.find(section => section.id === 'appearance-section');
      if (appearanceSection) {
        const backgroundBlurSetting = appearanceSection.items.find(item => item.id === 'background-blur');
        if (backgroundBlurSetting) {
          backgroundBlurSetting.value = backgroundBlurEnabled;
        }

        const clickToCloseSetting = appearanceSection.items.find(item => item.id === 'click-to-close');
        if (clickToCloseSetting) {
          clickToCloseSetting.value = clickToCloseEnabled;
        }
      }

      console.log('Setup: Appearance settings loaded:', { backgroundBlurEnabled, clickToCloseEnabled });
    } catch (e) {
      console.warn('Could not load appearance settings:', e);
    }

    // Create main elements
    const settingsButton = createSettingsButton();
    backdropElement = createBackdrop();
    const { panel } = createPanel();
    panelElement = panel;

    // Apply appearance settings after backdrop is created
    try {
      const savedAppearanceSettings = JSON.parse(localStorage.getItem('edvinity-appearance-settings') || '{}');
      const backgroundBlurEnabled = savedAppearanceSettings['background-blur'] !== false;
      const clickToCloseEnabledSetting = savedAppearanceSettings['click-to-close'] !== false;

      toggleBackgroundBlur(backgroundBlurEnabled);
      toggleClickToClose(clickToCloseEnabledSetting);
    } catch (e) {
      // Default to enabled if there's an error
      toggleBackgroundBlur(true);
      toggleClickToClose(true);
    }

    // Add keyboard shortcut handler
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Set up a listener for storage changes to sync settings across tabs
    window.addEventListener('storage', (event) => {
      if (event.key === 'edvinity-privacy-settings') {
        try {
          const newSettings = JSON.parse(event.newValue || '{}');

          // Create a function to update toggle states in the UI
          const updateToggleState = (settingId, isActive) => {
            const toggle = document.querySelector(`.edvinity-toggle[data-setting-id="${settingId}"]`);
            if (toggle) {
              // Update the toggle state based on the isActive parameter
              if (isActive) {
                toggle.classList.add('active');
                toggle.setAttribute('data-value', 'true');

                // Update the handle position
                const handle = toggle.querySelector('.edvinity-toggle-handle');
                if (handle) {
                  handle.style.transform = 'translate(26px, -50%)';
                }
              } else {
                toggle.classList.remove('active');
                toggle.setAttribute('data-value', 'false');

                // Reset the handle position
                const handle = toggle.querySelector('.edvinity-toggle-handle');
                if (handle) {
                  handle.style.transform = '';
                }
              }
            }
          };

          // Update settings and UI for each changed setting
          Object.keys(newSettings).forEach(key => {
            if (key in privacySettings && newSettings[key] !== privacySettings[key]) {
              // Update the setting value
              privacySettings[key] = newSettings[key];

              // Apply the functionality
              if (key === 'blur-info') {
                toggleBlurInfo(newSettings[key]);
              } else if (key === 'anti-logout') {
                toggleAntiLogout(newSettings[key]);
              } else if (key === 'auto-mute') {
                toggleAutoMute(newSettings[key]);
              } else if (key === 'auto-vocab') {
                // Update vocab settings from saved values
                vocabSettings.autoFill = privacySettings['vocab-auto-fill'] !== false;
                vocabSettings.autoPlay = privacySettings['vocab-auto-play'] !== false;
                vocabSettings.autoSubmit = privacySettings['vocab-auto-submit'] !== false;
                vocabSettings.delay = parseInt(privacySettings['vocab-delay']) || 1000;
                toggleAutoVocab(newSettings[key]);
              } else if (key === 'show-column') {
                toggleShowColumn(newSettings[key]);
              } else if (key.startsWith('vocab-')) {
                // Handle vocab sub-settings
                if (key === 'vocab-auto-fill') {
                  vocabSettings.autoFill = newSettings[key];
                } else if (key === 'vocab-auto-play') {
                  vocabSettings.autoPlay = newSettings[key];
                } else if (key === 'vocab-auto-submit') {
                  vocabSettings.autoSubmit = newSettings[key];
                } else if (key === 'vocab-delay') {
                  vocabSettings.delay = parseInt(newSettings[key]) || 1000;
                }
              }

              // Update the toggle state in the UI
              updateToggleState(key, newSettings[key]);
            }
          });
        } catch (e) {
          console.warn('Could not process storage event:', e);
        }
      } else if (event.key === 'edvinity-appearance-settings') {
        try {
          const newAppearanceSettings = JSON.parse(event.newValue || '{}');

          // Update the UI toggle state helper function
          const updateToggleState = (settingId, isActive) => {
            const toggle = document.querySelector(`.edvinity-toggle[data-setting-id="${settingId}"]`);
            if (toggle) {
              if (isActive) {
                toggle.classList.add('active');
                toggle.setAttribute('data-value', 'true');
                const handle = toggle.querySelector('.edvinity-toggle-handle');
                if (handle) {
                  handle.style.transform = 'translate(26px, -50%)';
                }
              } else {
                toggle.classList.remove('active');
                toggle.setAttribute('data-value', 'false');
                const handle = toggle.querySelector('.edvinity-toggle-handle');
                if (handle) {
                  handle.style.transform = '';
                }
              }
            }
          };

          // Handle background blur setting sync
          if ('background-blur' in newAppearanceSettings) {
            const backgroundBlurEnabled = newAppearanceSettings['background-blur'];
            toggleBackgroundBlur(backgroundBlurEnabled);
            updateToggleState('background-blur', backgroundBlurEnabled);
            console.log('Background blur setting synced from storage:', backgroundBlurEnabled);
          }

          // Handle click-to-close setting sync
          if ('click-to-close' in newAppearanceSettings) {
            const clickToCloseEnabled = newAppearanceSettings['click-to-close'];
            toggleClickToClose(clickToCloseEnabled);
            updateToggleState('click-to-close', clickToCloseEnabled);
            console.log('Click to close setting synced from storage:', clickToCloseEnabled);
          }
        } catch (e) {
          console.warn('Could not sync appearance settings from storage:', e);
        }
      }
    });

    // Create header with control buttons
    const { header, closeButton, minimizeButton, dragHandle } = createHeader();
    closeButton.addEventListener('click', hideMainPanel);

    // Setup minimize/maximize functionality

    const minimizePanel = () => {
      if (isMinimized) return;

      // Store current panel state before minimizing
      panelState = {
        left: panelElement.style.left,
        top: panelElement.style.top
      };

      // Hide backdrop when minimized
      backdropElement.classList.remove('visible');

      // Update minimize button icon
      minimizeButton.innerHTML = `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line><line x1="12" y1="5" x2="12" y2="19"></line></svg>`;

      // Add minimized class
      panelElement.classList.add('minimized');

      isMinimized = true;
    };

    const maximizePanel = () => {
      if (!isMinimized) return;

      // Show backdrop when maximized
      backdropElement.classList.add('visible');

      // Update minimize button icon
      minimizeButton.innerHTML = `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>`;

      // Remove minimized class
      panelElement.classList.remove('minimized');

      isMinimized = false;
    };

    minimizeButton.addEventListener('click', () => {
      if (isMinimized) {
        maximizePanel();
      } else {
        minimizePanel();
      }
    });

    // Setup draggable functionality
    setupDraggable(dragHandle, panelElement, header);

    // Force tabs mode initially
    currentUIMode = UI_MODES.TABS;

    // Create content based on UI mode
    let content;
    if (currentUIMode === UI_MODES.TABS) {
      // Create tabbed content
      content = createContent();
      // Add tabs-specific class
      panelElement.classList.add('edvinity-tabs-mode');
      panelElement.classList.remove('edvinity-panel-mode');

      // Set initial size for tabs mode
      document.documentElement.style.setProperty('--panel-width', 'var(--panel-width-tabs)');
      panelElement.style.marginLeft = 'calc(var(--panel-width-tabs) / -2)';
    } else {
      // Create panel content (vertical list)
      content = createElement('div', { className: 'edvinity-content edvinity-panel-content' });

      // Create all sections without dividers
      SETTINGS.forEach(section => {
        const sectionEl = createSection(section);
        content.appendChild(sectionEl);
      });

      // Add panel-specific class
      panelElement.classList.add('edvinity-panel-mode');
      panelElement.classList.remove('edvinity-tabs-mode');

      // Set initial size for panel mode
      document.documentElement.style.setProperty('--panel-width', 'var(--panel-width-regular)');
      panelElement.style.marginLeft = 'calc(var(--panel-width-regular) / -2)';
    }

    // Create footer
    const footer = createFooter();

    // Assemble the UI
    panelElement.append(header, content, footer);

    // Add to DOM
    document.body.append(settingsButton, backdropElement, panelElement);

    // Setup event listeners
    const buttonElement = settingsButton.querySelector('.edvinity-button');
    buttonElement.addEventListener('click', () => {
      if (!isOpen) {
        // First ensure the button is in expanded state for a smooth transition
        buttonElement.classList.add('expanded');

        // Slight delay before opening the panel for a smoother sequence
        setTimeout(() => {
          // Open the panel
          openSettings();

          // Remove expanded class after panel is visible
          setTimeout(() => {
            buttonElement.classList.remove('expanded');
          }, 500);
        }, 150);
      } else if (isMinimized) {
        // If panel is minimized, maximize it
        maximizePanel();
      } else {
        // If panel is already open and maximized, minimize it
        minimizePanel();
      }
    });

    backdropElement.addEventListener('click', (e) => {
      // Only close if clicking directly on the backdrop (not the panel) AND click-to-close is enabled
      if (e.target === backdropElement && clickToCloseEnabled) {
        // Always just hide the main panel without closing floating windows
        hideMainPanel();
      }
    });

    // Add document click listener for when backdrop is hidden (no-blur mode)
    document.addEventListener('click', (e) => {
      // Only handle clicks when panel is open, click-to-close is enabled, and backdrop is hidden
      if (isOpen && clickToCloseEnabled && backdropElement.classList.contains('no-blur')) {
        // Check if click is outside the panel
        if (!panelElement.contains(e.target) && !e.target.closest('.edvinity-button-container')) {
          hideMainPanel();
        }
      }
    });

    // Double-click on header to toggle minimize/maximize
    header.addEventListener('dblclick', (e) => {
      // Only toggle if clicking on the header itself or its direct children, not on buttons
      const isHeaderArea = e.target === header ||
                          e.target === dragHandle ||
                          e.target.classList.contains('edvinity-title-container') ||
                          e.target.classList.contains('edvinity-drag-indicator') ||
                          e.target.classList.contains('edvinity-title');

      if (isHeaderArea) {
        if (isMinimized) {
          maximizePanel();
        } else {
          minimizePanel();
        }
      }
    });
  };

  // Removed duplicate keyboard event listener - now handled by handleKeyboardShortcuts

  // --- Privacy Shield Implementation ---

  // Store settings state
  const privacySettings = {
    'blur-info': false,
    'anti-logout': false,
    'auto-mute': false,
    'auto-vocab': false,
    'vocab-auto-fill': true,
    'vocab-auto-play': true,
    'vocab-auto-submit': true,
    'vocab-delay': '1000',
    'show-column': false,
    'name-spoofer': false,
    'blur-profile-pics': false,
    'blur-student-ids': false
  };

  // Try to load saved settings immediately
  try {
    const savedSettings = JSON.parse(localStorage.getItem('edvinity-privacy-settings') || '{}');

    // Update privacy settings with saved values
    Object.keys(savedSettings).forEach(key => {
      if (key in privacySettings) {
        privacySettings[key] = savedSettings[key];
      }
    });

    console.log('Loaded initial privacy settings:', privacySettings);
  } catch (e) {
    console.warn('Could not load initial privacy settings:', e);
  }

  // Function to apply settings - UI only, no functionality
  const applySettings = (settingId, value) => {
    // No functionality, just UI
  };

  // Load saved settings - UI only, no functionality
  const loadPrivacySettings = () => {
    // No loading of settings
    // No applying settings
  };

  // Selectors for personal information elements
  const SELECTORS = {
    NAMES: [
      // Target the actual name elements from your HTML
      'h1.user-title',                                    // "a a's Profile" header
      'a.nav.dave[data-bind*="user().FullName"]',        // "a a" navigation link
      'a.nav.dave[data-bind*="FullName"]',               // Alternative selector
      'a.nav[data-bind*="user().FullName"]',             // Broader nav selector
      'a[data-bind*="user().FullName"]',                 // Even broader selector
      '[data-bind*="user().FullName"]'                   // Any element with FullName binding
    ],
    PROFILE_PICS: [
      'img.profile-pic',
      'img.avatar',
      'img.user-avatar',
      'img.profile-image',
      'div.profile-pic',
      'div.avatar',
      '.profile-button[id*="profile"]'
    ],
    STUDENT_INFO: [
      // Target the actual student data from the HTML structure
      '.profile-container dd.field-content',  // Student ID, school info, etc.
      '.profile-container .field-content',    // Alternative selector
      'dd.field-content',                     // Direct field content
      '.student-information-title',           // The "STUDENT INFORMATION" header
      'dt.field-label'                        // Field labels like "STUDENT ID", "GRADE", etc.
    ]
  };

  // Function to toggle blur on all info
  const toggleBlurInfo = (enabled) => {
    console.log('Blur toggle called with enabled:', enabled);
    console.log('Current BLUR_INTENSITY:', BLUR_INTENSITY);
    console.log('Current privacy settings:', privacySettings);

    if (enabled) {
      console.log('Blur Info enabled - checking which sub-options are enabled');

      // First, remove all existing blur to start fresh
      removeBlurFromElements(SELECTORS.NAMES.join(','));
      removeBlurFromElements(SELECTORS.PROFILE_PICS.join(','));
      removeBlurFromElements(SELECTORS.STUDENT_INFO.join(','));

      // Only apply blur based on enabled sub-options
      // Note: name-spoofer doesn't use blur, it replaces text, so we skip it here
      // Name spoofing is handled separately by applyNameSpoofing function

      if (privacySettings['blur-profile-pics'] === true) {
        console.log('Sub-option enabled: applying blur to profile pics');
        applyBlurToElements(SELECTORS.PROFILE_PICS.join(','), BLUR_INTENSITY);
      }

      if (privacySettings['blur-student-ids'] === true) {
        console.log('Sub-option enabled: applying blur to student info');
        applyBlurToElements(SELECTORS.STUDENT_INFO.join(','), BLUR_INTENSITY);
      }

      console.log('Blur Info setup complete - only enabled sub-options should be blurred');

    } else {
      console.log('Removing blur from all elements');
      // Remove blur from all elements when blur-info is disabled
      removeBlurFromElements(SELECTORS.NAMES.join(','));
      removeBlurFromElements(SELECTORS.PROFILE_PICS.join(','));
      removeBlurFromElements(SELECTORS.STUDENT_INFO.join(','));
    }
  };

  // Helper function to apply specific blur types
  const applySpecificBlur = (blurType, enabled) => {
    console.log(`Applying specific blur: ${blurType}, enabled: ${enabled}`);

    // Only apply if main blur-info is enabled
    if (!privacySettings['blur-info']) {
      console.log('Main blur-info is disabled, skipping specific blur');
      return;
    }

    if (blurType === 'profile-pics') {
      if (enabled) {
        console.log('Applying blur to profile pics');
        applyBlurToElements(SELECTORS.PROFILE_PICS.join(','), BLUR_INTENSITY);
      } else {
        console.log('Removing blur from profile pics');
        removeBlurFromElements(SELECTORS.PROFILE_PICS.join(','));
      }
    } else if (blurType === 'student-ids') {
      if (enabled) {
        console.log('Applying blur to student IDs');
        applyBlurToElements(SELECTORS.STUDENT_INFO.join(','), BLUR_INTENSITY);
      } else {
        console.log('Removing blur from student IDs');
        removeBlurFromElements(SELECTORS.STUDENT_INFO.join(','));
      }
    } else if (blurType === 'names') {
      if (enabled) {
        console.log('Applying blur to names');
        applyBlurToElements(SELECTORS.NAMES.join(','), BLUR_INTENSITY);
      } else {
        console.log('Removing blur from names');
        removeBlurFromElements(SELECTORS.NAMES.join(','));
      }
    }
  };

  // Function to apply name spoofing
  const applyNameSpoofing = (enabled, mode = 'preset', value = 'student') => {
    console.log('Name spoofing called:', { enabled, mode, value });

    if (enabled) {
      let replacementText;

      if (mode === 'custom') {
        replacementText = value || 'Anonymous';
      } else {
        // Handle preset modes with proper replacements
        const presetReplacements = {
          'student': 'Student',
          'user': 'User',
          'anonymous': 'Anonymous',
          'redacted': '[REDACTED]'
        };
        replacementText = presetReplacements[value] || 'Student';
      }

      console.log('Name spoofing replacement text:', replacementText);
      console.log('Name spoofing selectors:', SELECTORS.NAMES);

      // Apply name spoofing to all name elements
      SELECTORS.NAMES.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          console.log(`Found ${elements.length} elements for selector: ${selector}`);

          elements.forEach((el, index) => {
            console.log('Processing element:', el, 'Text:', el.textContent);

            // Skip elements that are already spoofed or don't contain actual names
            if (el.hasAttribute('data-original-text')) {
              console.log('Element already spoofed, skipping');
              return;
            }

            if (!el.textContent.trim()) {
              console.log('Element has no text content, skipping');
              return;
            }

            // Store original text
            el.setAttribute('data-original-text', el.textContent);

            // Smart replacement that preserves structure while being discreet
            let newText = el.textContent;

            // For profile headers like "a a's Profile" -> "Student's Profile"
            if (newText.includes("'s Profile")) {
              newText = `${replacementText}'s Profile`;
            }
            // For simple names like "a a" -> use replacement text but keep it short for discretion
            else if (newText.match(/^[a-zA-Z]\s[a-zA-Z]$/)) {
              // For very short patterns, use initials to be discreet
              const firstInitial = replacementText.charAt(0).toUpperCase();
              const secondInitial = replacementText.length > 1 ? replacementText.charAt(1).toUpperCase() : 'S';
              newText = `${firstInitial} ${secondInitial}`;
            }
            // For initials like "AA" -> use replacement initials
            else if (newText.match(/^[A-Z]{2,}$/)) {
              const firstInitial = replacementText.charAt(0).toUpperCase();
              const secondInitial = replacementText.length > 1 ? replacementText.charAt(1).toUpperCase() : 'S';
              newText = firstInitial + secondInitial;
            }
            // For single letters -> first initial of replacement
            else if (newText.match(/^[a-zA-Z]$/)) {
              newText = replacementText.charAt(0).toUpperCase();
            }
            // For longer text patterns, use the full replacement text
            else {
              newText = replacementText;
            }

            el.textContent = newText;
            el.classList.add('edvinity-spoofed-name');
            console.log('Applied spoofing to element:', el.textContent);
          });
        } catch (e) {
          console.warn('Could not apply name spoofing to selector:', selector, e);
        }
      });

      // Also try broader selectors if the specific ones don't work
      const broadSelectors = [
        '.profile-button',
        '[data-bind*="FullName"]',
        '.user-name',
        '.username'
      ];

      broadSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          console.log(`Broad selector ${selector} found ${elements.length} elements`);

          elements.forEach((el, index) => {
            if (!el.hasAttribute('data-original-text') && el.textContent.trim()) {
              el.setAttribute('data-original-text', el.textContent);

              let newText = el.textContent;

              // Smart replacement for broad selectors
              if (newText.includes("'s Profile")) {
                newText = `${replacementText}'s Profile`;
              } else if (newText.match(/^[a-zA-Z]\s[a-zA-Z]$/)) {
                // For very short patterns, use initials to be discreet
                const firstInitial = replacementText.charAt(0).toUpperCase();
                const secondInitial = replacementText.length > 1 ? replacementText.charAt(1).toUpperCase() : 'S';
                newText = `${firstInitial} ${secondInitial}`;
              } else if (newText.match(/^[A-Z]{2,}$/)) {
                const firstInitial = replacementText.charAt(0).toUpperCase();
                const secondInitial = replacementText.length > 1 ? replacementText.charAt(1).toUpperCase() : 'S';
                newText = firstInitial + secondInitial;
              } else if (newText.match(/^[a-zA-Z]$/)) {
                newText = replacementText.charAt(0).toUpperCase();
              } else {
                newText = replacementText;
              }

              el.textContent = newText;
              el.classList.add('edvinity-spoofed-name');
              console.log('Applied broad spoofing to:', el.textContent);
            }
          });
        } catch (e) {
          console.warn('Could not apply broad name spoofing to selector:', selector, e);
        }
      });

    } else {
      console.log('Removing name spoofing');
      // Remove name spoofing
      document.querySelectorAll('.edvinity-spoofed-name').forEach(el => {
        const originalText = el.getAttribute('data-original-text');
        if (originalText) {
          el.textContent = originalText;
          el.removeAttribute('data-original-text');
        }
        el.classList.remove('edvinity-spoofed-name');
        console.log('Removed spoofing from element, restored to:', el.textContent);
      });
    }
  };

  // Dynamic blur intensity - can be changed by user
  let BLUR_INTENSITY = 'medium';

  // Helper function to sanitize blur intensity values
  const sanitizeBlurIntensity = (intensity) => {
    const validIntensities = ['low', 'medium', 'high'];
    let sanitizedIntensity = intensity;

    // If intensity contains spaces or invalid characters, extract the valid part
    if (typeof intensity === 'string' && intensity.includes(' ')) {
      // Extract the first word (should be low, medium, or high)
      sanitizedIntensity = intensity.split(' ')[0].toLowerCase();
    }

    // Ensure it's one of the valid values
    if (!validIntensities.includes(sanitizedIntensity)) {
      console.warn('Invalid blur intensity:', intensity, 'defaulting to medium');
      sanitizedIntensity = 'medium';
    }

    return sanitizedIntensity;
  };

  // Load blur intensity from settings
  try {
    const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
    if (blurSettings['blur-intensity']) {
      BLUR_INTENSITY = sanitizeBlurIntensity(blurSettings['blur-intensity']);
    }
  } catch (e) {
    console.warn('Could not load blur intensity setting:', e);
  }

  // Set on window for global access
  window.BLUR_INTENSITY = BLUR_INTENSITY;

  // Anti-logout functionality
  let antiLogoutInterval = null;

  // Auto-mute functionality
  let autoMuteObserver = null;

  // Function to toggle auto-mute functionality
  const toggleAutoMute = (enabled) => {
    if (enabled) {
      // Start the auto-mute mechanism
      startAutoMute();
    } else {
      // Stop the auto-mute mechanism
      stopAutoMute();
    }
  };

  // Function to start the auto-mute mechanism
  const startAutoMute = () => {
    // Clear any existing observer
    stopAutoMute();

    // Mute all existing audio and video elements
    muteAllMedia();

    // Create a new observer to watch for new media elements
    autoMuteObserver = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            // Check if the node is an audio or video element
            if (node.tagName === 'AUDIO' || node.tagName === 'VIDEO') {
              // Mute the element
              node.muted = true;
            }

            // Also check children of the node
            if (node.querySelectorAll) {
              const mediaElements = node.querySelectorAll('audio, video');
              mediaElements.forEach(el => {
                el.muted = true;
              });
            }
          });
        }
      });
    });

    // Start observing the document
    autoMuteObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Store the observer in a global variable to prevent garbage collection
    window.edvinityAutoMuteObserver = autoMuteObserver;

    // Also handle iframes
    handleIframesForAutoMute(true);

    // Log that auto-mute has been started
    console.log('Auto-mute: Started');
  };

  // Function to stop the auto-mute mechanism
  const stopAutoMute = () => {
    if (autoMuteObserver) {
      autoMuteObserver.disconnect();
      autoMuteObserver = null;

      // Unmute all media elements
      unmuteAllMedia();

      // Handle iframes
      handleIframesForAutoMute(false);

      // Log that auto-mute has been stopped
      console.log('Auto-mute: Stopped');
    }
  };

  // Function to unmute all media elements
  const unmuteAllMedia = () => {
    // Unmute all audio elements
    document.querySelectorAll('audio').forEach(audio => {
      audio.muted = false;
    });

    // Unmute all video elements
    document.querySelectorAll('video').forEach(video => {
      video.muted = false;
    });

    // Also unmute media in iframes
    try {
      document.querySelectorAll('iframe').forEach(iframe => {
        try {
          if (iframe.contentDocument) {
            iframe.contentDocument.querySelectorAll('audio, video').forEach(media => {
              media.muted = false;
            });
          }
        } catch (e) {
          console.warn('Could not access iframe content (cross-origin):', e);
        }
      });
    } catch (e) {
      console.warn('Error unmuting media in iframes:', e);
    }
  };

  // Function to mute all media elements
  const muteAllMedia = () => {
    // Mute all audio elements
    document.querySelectorAll('audio').forEach(audio => {
      audio.muted = true;
    });

    // Mute all video elements
    document.querySelectorAll('video').forEach(video => {
      video.muted = true;
    });

    // Also mute media in iframes
    try {
      document.querySelectorAll('iframe').forEach(iframe => {
        try {
          if (iframe.contentDocument) {
            iframe.contentDocument.querySelectorAll('audio, video').forEach(media => {
              media.muted = true;
            });
          }
        } catch (e) {
          console.warn('Could not access iframe content (cross-origin):', e);
        }
      });
    } catch (e) {
      console.warn('Error muting media in iframes:', e);
    }
  };

  // Function to handle iframes for auto-mute
  const handleIframesForAutoMute = (enable) => {
    document.querySelectorAll('iframe').forEach(iframe => {
      try {
        if (iframe.contentDocument) {
          if (enable) {
            // Mute all media in the iframe
            iframe.contentDocument.querySelectorAll('audio, video').forEach(media => {
              media.muted = true;
            });

            // Set up an observer for the iframe
            const iframeObserver = new MutationObserver((mutations) => {
              mutations.forEach(mutation => {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                  mutation.addedNodes.forEach(node => {
                    if (node.tagName === 'AUDIO' || node.tagName === 'VIDEO') {
                      node.muted = true;
                    }

                    if (node.querySelectorAll) {
                      node.querySelectorAll('audio, video').forEach(el => {
                        el.muted = true;
                      });
                    }
                  });
                }
              });
            });

            // Start observing the iframe document
            iframeObserver.observe(iframe.contentDocument.body, {
              childList: true,
              subtree: true
            });

            // Store the observer to prevent garbage collection
            if (!window.edvinityIframeAutoMuteObservers) {
              window.edvinityIframeAutoMuteObservers = [];
            }
            window.edvinityIframeAutoMuteObservers.push(iframeObserver);
          } else {
            // Unmute all media in the iframe when disabling
            iframe.contentDocument.querySelectorAll('audio, video').forEach(media => {
              media.muted = false;
            });

            // Disconnect any observers for this iframe
            if (window.edvinityIframeAutoMuteObservers && window.edvinityIframeAutoMuteObservers.length > 0) {
              window.edvinityIframeAutoMuteObservers.forEach(observer => {
                observer.disconnect();
              });
              window.edvinityIframeAutoMuteObservers = [];
            }
          }
        }
      } catch (e) {
        console.warn('Could not access iframe content (cross-origin):', e);
      }
    });
  };




  // Function to toggle anti-logout functionality
  const toggleAntiLogout = (enabled) => {
    if (enabled) {
      // Start the anti-logout mechanism
      startAntiLogout();
    } else {
      // Stop the anti-logout mechanism
      stopAntiLogout();
    }
  };

  // Function to start the anti-logout mechanism
  const startAntiLogout = () => {
    // Clear any existing interval
    stopAntiLogout();

    // Create a new interval that simulates user activity every 30 seconds
    // This ensures we keep the session active even with aggressive timeouts
    antiLogoutInterval = setInterval(() => {
      // Simulate user activity by creating and triggering a mouse movement
      simulateUserActivity();

      // Log activity (only visible in console for debugging)
      console.log('Anti-logout: Activity simulated at ' + new Date().toLocaleTimeString());
    }, 30 * 1000); // 30 seconds in milliseconds

    // Store the interval ID in a global variable to prevent garbage collection
    window.edvinityAntiLogoutInterval = antiLogoutInterval;

    // Log that anti-logout has been started
    console.log('Anti-logout: Started');
  };

  // Function to stop the anti-logout mechanism
  const stopAntiLogout = () => {
    if (antiLogoutInterval) {
      clearInterval(antiLogoutInterval);
      antiLogoutInterval = null;

      // Log that anti-logout has been stopped
      console.log('Anti-logout: Stopped');
    }
  };

  // Function to simulate user activity
  const simulateUserActivity = () => {
    try {
      // Create a new MouseEvent - Firefox compatible version
      let event;
      try {
        // Try the modern approach first
        event = new MouseEvent('mousemove', {
          bubbles: true,
          cancelable: true,
          clientX: Math.floor(Math.random() * window.innerWidth),
          clientY: Math.floor(Math.random() * window.innerHeight)
        });
      } catch (e) {
        // Fallback for older browsers or strict implementations
        event = document.createEvent('MouseEvent');
        event.initMouseEvent(
          'mousemove', // type
          true,        // bubbles
          true,        // cancelable
          window,      // view
          0,           // detail
          Math.floor(Math.random() * window.innerWidth),  // screenX
          Math.floor(Math.random() * window.innerHeight), // screenY
          Math.floor(Math.random() * window.innerWidth),  // clientX
          Math.floor(Math.random() * window.innerHeight), // clientY
          false,       // ctrlKey
          false,       // altKey
          false,       // shiftKey
          false,       // metaKey
          0,           // button
          null         // relatedTarget
        );
      }

      // Dispatch the event on the document
      document.dispatchEvent(event);

      // Also try to interact with any potential session-keeping API endpoints
      // This is a common approach for systems that track session activity server-side
      try {
        // Create a hidden image that loads a resource from the server
        // This will trigger a network request without changing the page
        const img = new Image();
        img.style.display = 'none';
        img.src = window.location.href.split('?')[0] + '?activity=' + Date.now();
        document.body.appendChild(img);

        // Remove the image after it has loaded or failed
        setTimeout(() => {
          if (img && img.parentNode) {
            img.parentNode.removeChild(img);
          }
        }, 5000);
      } catch (e) {
        // Ignore errors from the network request
        console.warn('Anti-logout: Failed to make network request', e);
      }
    } catch (e) {
      console.warn('Anti-logout: Failed to simulate user activity', e);
    }
  };

  // Auto Vocabulary functionality
  let autoVocabInterval = null;
  let vocabSettings = {
    enabled: false,
    autoFill: true,
    autoPlay: true,
    autoSubmit: true,
    delay: 1000
  };

  // Track vocabulary state to prevent duplicate actions
  let vocabState = {
    lastProcessedWord: '',
    lastActionTime: 0,
    processingInProgress: false,
    retryCount: 0,
    maxRetries: 3
  };

  // Function to toggle auto vocabulary functionality
  const toggleAutoVocab = (enabled) => {
    console.log('Auto Vocab: Toggle called with enabled =', enabled);
    vocabSettings.enabled = enabled;

    if (enabled) {
      console.log('Auto Vocab: Starting auto vocabulary functionality');
      // Reset state when enabling
      vocabState = {
        lastProcessedWord: '',
        lastActionTime: 0,
        processingInProgress: false,
        retryCount: 0,
        maxRetries: 3
      };
      startAutoVocab();
    } else {
      console.log('Auto Vocab: Stopping auto vocabulary functionality');
      stopAutoVocab();
    }
  };

  // Function to toggle background blur functionality
  const toggleBackgroundBlur = (enabled) => {
    console.log('Background blur toggle:', enabled);

    if (backdropElement) {
      if (enabled) {
        // Enable background blur
        backdropElement.classList.remove('no-blur');
        console.log('Background blur: Enabled');
      } else {
        // Disable background blur
        backdropElement.classList.add('no-blur');
        console.log('Background blur: Disabled');
      }
    }
  };

  // Variable to track click-to-close state
  let clickToCloseEnabled = true;

  // Function to toggle click-to-close functionality
  const toggleClickToClose = (enabled) => {
    console.log('Click to close toggle:', enabled);
    clickToCloseEnabled = enabled;

    if (enabled) {
      console.log('Click to close: Enabled');
    } else {
      console.log('Click to close: Disabled');
    }
  };

  // Function to start auto vocabulary
  const startAutoVocab = () => {
    if (autoVocabInterval) {
      clearInterval(autoVocabInterval);
    }

    // Check every 3 seconds for vocabulary activities (increased from 2 seconds for better performance)
    autoVocabInterval = setInterval(() => {
      if (vocabSettings.enabled && !vocabState.processingInProgress) {
        handleVocabularyActivity();
      }
    }, 3000);

    console.log('Auto Vocab: Started monitoring for vocabulary activities');
  };

  // Function to stop auto vocabulary
  const stopAutoVocab = () => {
    if (autoVocabInterval) {
      clearInterval(autoVocabInterval);
      autoVocabInterval = null;
      vocabState.processingInProgress = false;
      console.log('Auto Vocab: Stopped monitoring');
    }
  };

  // Main vocabulary handling function
  const handleVocabularyActivity = async () => {
    // Prevent concurrent processing
    if (vocabState.processingInProgress) {
      console.log('Auto Vocab: Processing already in progress, skipping');
      return;
    }

    try {
      vocabState.processingInProgress = true;

      // Check if we're on a vocabulary activity with more flexible matching
      const activityTitle = document.getElementById("activity-title")?.innerText?.toLowerCase();
      if (!activityTitle) {
        console.log('Auto Vocab: No activity title found');
        return;
      }

      // More flexible vocabulary activity detection
      const isVocabActivity = activityTitle.includes("vocabulary") ||
                             activityTitle.includes("vocab") ||
                             activityTitle.includes("lesson") ||
                             activityTitle.includes("review");

      if (!isVocabActivity) {
        console.log('Auto Vocab: Not a vocabulary activity:', activityTitle);
        return;
      }

      console.log('Auto Vocab: Vocabulary activity detected:', activityTitle);

      // Get the iframe content with better error handling
      const frame = window.frames[0];
      if (!frame) {
        console.log('Auto Vocab: No iframe found');
        vocabState.retryCount++;
        if (vocabState.retryCount >= vocabState.maxRetries) {
          console.warn('Auto Vocab: Max retries reached for iframe access');
          vocabState.retryCount = 0;
        }
        return;
      }

      let iframeDoc;
      try {
        iframeDoc = frame.document;
      } catch (e) {
        console.log('Auto Vocab: Cannot access iframe document (cross-origin?):', e.message);
        return;
      }

      if (!iframeDoc) {
        console.log('Auto Vocab: Iframe document is null');
        return;
      }

      // Reset retry count on successful iframe access
      vocabState.retryCount = 0;

      await processVocabularyElements(iframeDoc);

    } catch (error) {
      console.error('Auto Vocab: Error in handleVocabularyActivity:', error);
      vocabState.retryCount++;
    } finally {
      vocabState.processingInProgress = false;
    }
  };

  // Process vocabulary elements in the iframe
  const processVocabularyElements = async (iframeDoc) => {
    const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

    try {
      // Pre-hide textboxes immediately to prevent flicker during transitions
      const allTextboxes = iframeDoc.querySelectorAll("input[type='text'], textarea, [class*='textbox'], .word-textbox");
      allTextboxes.forEach(textbox => {
        if (!textbox.hasAttribute('data-edvinity-protected')) {
          textbox.style.transition = 'none';
          textbox.setAttribute('data-edvinity-protected', 'true');
        }
      });

      // Find vocabulary elements with improved selectors
      const wordBackground = iframeDoc.querySelector(".word-background") ||
                            iframeDoc.querySelector("[class*='word-background']") ||
                            iframeDoc.querySelector("[id*='word-background']");

      const wordTextbox = iframeDoc.querySelector(".word-textbox") ||
                         iframeDoc.querySelector("input[type='text']") ||
                         iframeDoc.querySelector("textarea") ||
                         iframeDoc.querySelector("[class*='textbox']");

      const nextButton = iframeDoc.querySelector(".uibtn.uibtn-blue.uibtn-arrow-next") ||
                        iframeDoc.querySelector("[class*='next']") ||
                        iframeDoc.querySelector("button[onclick*='next']") ||
                        iframeDoc.querySelector("a[onclick*='next']");

      const playButtons = Array.from(
        iframeDoc.querySelectorAll(".playbutton.vocab-play") ||
        iframeDoc.querySelectorAll("[class*='play']") ||
        iframeDoc.querySelectorAll("button[onclick*='play']") ||
        []
      );

      // Improved submit button detection with priority order
      let submitButton = null;
      const submitSelectors = [
        'a.uibtn.uibtn-blue.uibtn-med[data-bind*="submit"]',
        'button[data-bind*="submit"]',
        'a.uibtn.uibtn-blue.uibtn-med',
        '[data-bind*="submit"]',
        'button[onclick*="submit"]',
        'a[onclick*="submit"]',
        'input[type="submit"]',
        'button[type="submit"]'
      ];

      for (const selector of submitSelectors) {
        submitButton = iframeDoc.querySelector(selector);
        if (submitButton) break;
      }

      // Helper function to check element visibility
      const isElementVisible = (element) => {
        if (!element) return false;
        const style = window.getComputedStyle(element);
        return element.offsetParent !== null &&
               style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               !element.hidden;
      };

      const submitVisible = isElementVisible(submitButton);
      const nextVisible = isElementVisible(nextButton);

      console.log('Auto Vocab: Found elements:', {
        wordBackground: !!wordBackground,
        wordTextbox: !!wordTextbox,
        nextButton: !!nextButton,
        nextVisible: nextVisible,
        playButtons: playButtons.length,
        submitButton: !!submitButton,
        submitVisible: submitVisible,
        textboxValue: wordTextbox?.value || 'empty'
      });

      // Get current word text for state tracking
      const currentWordText = wordBackground?.value || wordBackground?.innerText?.trim() || '';

      // Check if we've already processed this word recently
      const now = Date.now();
      if (currentWordText === vocabState.lastProcessedWord &&
          (now - vocabState.lastActionTime) < (vocabSettings.delay * 2)) {
        console.log('Auto Vocab: Word already processed recently, skipping');
        return;
      }

      // Auto-fill the vocab text box
      if (vocabSettings.autoFill && wordBackground && wordTextbox && !wordTextbox.value.trim()) {
        const textToFill = wordBackground.value || wordBackground.innerText?.trim();
        if (textToFill && textToFill.length > 0) {
          try {
            // Use requestAnimationFrame for smoother transitions
            await new Promise(resolve => {
              requestAnimationFrame(() => {
                // Disable transitions and hide textbox
                const originalTransition = wordTextbox.style.transition;
                const originalVisibility = wordTextbox.style.visibility;

                wordTextbox.style.transition = 'none';
                wordTextbox.style.visibility = 'hidden';

                // Set the value instantly
                wordTextbox.value = textToFill;

                // Trigger events to ensure the change is registered
                wordTextbox.dispatchEvent(new Event("keyup", { bubbles: true }));
                wordTextbox.dispatchEvent(new Event("input", { bubbles: true }));
                wordTextbox.dispatchEvent(new Event("change", { bubbles: true }));

                // Force a reflow to ensure the value is set
                wordTextbox.offsetHeight;

                // Restore visibility and transitions in next frame
                requestAnimationFrame(() => {
                  wordTextbox.style.visibility = originalVisibility;
                  wordTextbox.style.transition = originalTransition;
                  resolve();
                });
              });
            });

            console.log('Auto Vocab: Filled text box with:', textToFill);
            vocabState.lastActionTime = now;
            await delay(vocabSettings.delay);
          } catch (e) {
            console.warn('Auto Vocab: Error filling textbox:', e);
          }
        }
      }

      // Auto-play audio buttons
      if (vocabSettings.autoPlay && playButtons.length > 0) {
        // Check if this is a new word by comparing the current word text
        const lastWordText = iframeDoc.body.getAttribute('data-last-word-text') || '';

        // If this is a new word, clear all previous click markers
        if (currentWordText && currentWordText !== lastWordText) {
          console.log('Auto Vocab: New word detected, clearing previous click markers');
          playButtons.forEach(btn => btn.removeAttribute('data-auto-clicked'));
          iframeDoc.body.setAttribute('data-last-word-text', currentWordText);
        }

        for (const button of playButtons) {
          if (!button.hasAttribute('data-auto-clicked') && isElementVisible(button)) {
            try {
              button.click();
              button.setAttribute('data-auto-clicked', 'true');
              console.log('Auto Vocab: Clicked play button for word:', currentWordText);
              vocabState.lastActionTime = Date.now();
              await delay(vocabSettings.delay);
            } catch (e) {
              console.warn('Auto Vocab: Error clicking play button:', e);
            }
          }
        }
      }

      // Auto-submit when ready
      if (vocabSettings.autoSubmit) {
        const isTextboxFilled = wordTextbox && wordTextbox.value.trim();
        const allPlaysPressed = playButtons.length === 0 || playButtons.every(btn => btn.hasAttribute('data-auto-clicked'));

        if (isTextboxFilled && allPlaysPressed) {
          await delay(vocabSettings.delay);

          // Determine which button to click based on visibility and priority
          let buttonToClick = null;
          let actionDescription = '';

          if (submitVisible && submitButton) {
            buttonToClick = submitButton;
            actionDescription = 'Submit button (visible)';
          } else if (nextVisible && nextButton) {
            buttonToClick = nextButton;
            actionDescription = 'Next button';
          } else if (submitButton) {
            buttonToClick = submitButton;
            actionDescription = 'Submit button (fallback)';
          }

          if (buttonToClick) {
            try {
              // Pre-hide all textboxes before clicking to prevent flicker on next word
              const allTextboxes = iframeDoc.querySelectorAll("input[type='text'], textarea, [class*='textbox'], .word-textbox");
              allTextboxes.forEach(textbox => {
                textbox.style.transition = 'none';
                textbox.style.visibility = 'hidden';
              });

              buttonToClick.click();
              console.log(`Auto Vocab: Clicked ${actionDescription}`);
              vocabState.lastProcessedWord = currentWordText;
              vocabState.lastActionTime = Date.now();

              // Set a timeout to restore visibility after page transition
              setTimeout(() => {
                try {
                  const newTextboxes = iframeDoc.querySelectorAll("input[type='text'], textarea, [class*='textbox'], .word-textbox");
                  newTextboxes.forEach(textbox => {
                    textbox.style.visibility = 'visible';
                  });
                } catch (e) {
                  console.warn('Auto Vocab: Error restoring textbox visibility:', e);
                }
              }, 100);

            } catch (e) {
              console.warn(`Auto Vocab: Error clicking ${actionDescription}:`, e);
            }
          } else {
            console.log('Auto Vocab: No suitable button found to click');
          }
        }
      }

    } catch (error) {
      console.error('Auto Vocab: Error in processVocabularyElements:', error);
    }
  };

  // Show Column functionality
  let showColumnInterval = null;

  // Function to toggle show column functionality
  const toggleShowColumn = (enabled) => {
    console.log('Show Column: Toggle called with enabled =', enabled);

    if (enabled) {
      console.log('Show Column: Starting show column functionality');
      startShowColumn();
    } else {
      console.log('Show Column: Stopping show column functionality');
      stopShowColumn();
    }
  };

  // Function to start show column monitoring
  const startShowColumn = () => {
    if (showColumnInterval) {
      clearInterval(showColumnInterval);
    }

    // Check every 2 seconds for activities that support column display
    showColumnInterval = setInterval(() => {
      handleShowColumn();
    }, 2000);

    console.log('Show Column: Started monitoring for supported activities');
  };

  // Function to stop show column monitoring
  const stopShowColumn = () => {
    if (showColumnInterval) {
      clearInterval(showColumnInterval);
      showColumnInterval = null;
      console.log('Show Column: Stopped monitoring');
    }
  };

  // Main show column handling function
  const handleShowColumn = () => {
    try {
      // Check if we're on a supported activity
      const activityTitle = document.getElementById("activity-title")?.innerText;
      if (!activityTitle) {
        return;
      }

      console.log('Show Column: Current activity:', activityTitle);

      // Handle different activity types
      if (activityTitle === 'Glossary') {
        handleGlossaryColumn();
      } else if (['Assignment', 'Instruction', 'Practice'].includes(activityTitle)) {
        handleActivityColumns();
      }

    } catch (error) {
      console.error('Show Column: Error in handleShowColumn:', error);
    }
  };

  // Handle glossary column display
  const handleGlossaryColumn = () => {
    try {
      const iframe = window.frames[0];
      if (iframe && iframe.document) {
        const vocabMenu = iframe.document.getElementById('vocab-menu');
        if (vocabMenu && !vocabMenu.hasAttribute('data-auto-clicked')) {
          vocabMenu.click();
          vocabMenu.setAttribute('data-auto-clicked', 'true');
          console.log('Show Column: Clicked vocab menu in glossary');
        }
      }
    } catch (error) {
      console.warn('Show Column: Could not handle glossary column:', error);
    }
  };

  // Handle activity columns (right and left columns)
  const handleActivityColumns = () => {
    try {
      // Try to access nested iframe structure
      const outerFrame = window.frames[0];
      if (!outerFrame) return;

      const innerFrame = outerFrame.frames[0];
      if (!innerFrame || !innerFrame.document) return;

      const innerDoc = innerFrame.document;

      // Show right column
      try {
        const rightColumn = innerDoc.getElementsByClassName("right-column")[0];
        if (rightColumn && rightColumn.children[0]) {
          const rightColumnContent = rightColumn.children[0];
          if (rightColumnContent.style.display !== "block") {
            rightColumnContent.style.display = "block";
            console.log('Show Column: Right column displayed');
          }
        }
      } catch (error) {
        console.warn('Show Column: Error showing right column:', error);
      }

      // Show left column
      try {
        const leftColumn = innerDoc.getElementsByClassName("left-column")[0];
        if (leftColumn && leftColumn.children[0]) {
          const leftColumnContent = leftColumn.children[0];
          if (leftColumnContent.style.display !== "block") {
            leftColumnContent.style.display = "block";
            console.log('Show Column: Left column displayed');
          }
        }
      } catch (error) {
        console.warn('Show Column: Error showing left column:', error);
      }

    } catch (error) {
      console.warn('Show Column: Could not access iframe structure:', error);
    }
  };

  // Auto-graded functionality
  // Function to toggle auto-graded functionality
  const toggleAutoGraded = (enabled) => {
    console.log('Auto-graded: Toggle called with enabled =', enabled);

    if (enabled) {
      // Start the auto-graded mechanism
      startAutoGraded();
    } else {
      // Stop the auto-graded mechanism
      stopAutoGraded();
    }
  };

  // Simple console logging function to replace the debug panel
  const logToDebugPanel = (message, isError = false) => {
    if (isError) {
      console.error('Auto-graded:', message);
    } else {
      console.log('Auto-graded:', message);
    }
  };

  // Function to start the auto-graded mechanism
  const startAutoGraded = () => {
    // Log to console
    console.log('Auto-graded: Starting mechanism');

    // Clear any existing observer
    stopAutoGraded();

    // Add a debounce mechanism to prevent too frequent checks
    let debounceTimer = null;
    let lastCheckTime = 0;
    const debounceDelay = 1000; // 1 second debounce
    const minCheckInterval = 3000; // 3 seconds minimum between checks

    // Create a new observer to watch for assessment pages
    autoGradedObserver = new MutationObserver((mutations) => {
      // Only check if there were meaningful changes
      const shouldCheck = mutations.some(mutation => {
        return mutation.addedNodes.length > 0 ||
               mutation.removedNodes.length > 0 ||
               mutation.type === 'attributes';
      });

      if (shouldCheck) {
        // Check if enough time has passed since the last check
        const now = Date.now();
        if (now - lastCheckTime < minCheckInterval) {
          // If we've checked recently, debounce the next check
          clearTimeout(debounceTimer);
          debounceTimer = setTimeout(() => {
            console.log('Auto-graded: DOM changes detected, checking for assessment page (debounced)');
            lastCheckTime = Date.now();
            checkForAssessmentPage();
          }, debounceDelay);
        } else {
          // If enough time has passed, check immediately
          console.log('Auto-graded: DOM changes detected, checking for assessment page');
          lastCheckTime = now;
          checkForAssessmentPage();
        }
      }
    });

    // Start observing the document with more specific targets
    autoGradedObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false, // Don't watch all attributes, too noisy
      attributeFilter: ['id', 'class'] // Only watch these attributes
    });

    // Store the observer in a global variable to prevent garbage collection
    window.edvinityAutoGradedObserver = autoGradedObserver;

    // Set up a message event listener to receive question IDs from iframes
    const autoGradedMessageHandler = function(event) {
      // Check if the message is from our script in an iframe
      if (event.data && event.data.type === 'edvinity-auto-graded-question-ids') {
        console.log('Auto-graded: Received question IDs from iframe:', event.data.questionIds);

        // Process the question IDs
        if (event.data.questionIds && event.data.questionIds.length > 0) {
          // Process questions one by one with a delay to avoid overwhelming the API
          processQuestionsSequentially(event.data.questionIds, document);
        }
      }
    };

    // Add the message event listener
    window.addEventListener('message', autoGradedMessageHandler);

    // Store the message event listener for later removal
    window._edvinityAutoGradedMessageHandler = autoGradedMessageHandler;

    // Check immediately if we're already on an assessment page
    console.log('Auto-graded: Checking immediately for assessment page');
    lastCheckTime = Date.now();
    checkForAssessmentPage();

    // Also handle iframes
    console.log('Auto-graded: Setting up iframe handling');
    handleIframesForAutoGraded(true);

    // Log that auto-graded has been started
    console.log('Auto-graded: Started');
  };

  // Function to stop the auto-graded mechanism
  const stopAutoGraded = () => {
    if (autoGradedObserver) {
      autoGradedObserver.disconnect();
      autoGradedObserver = null;

      // Remove the message event listener
      if (window._edvinityAutoGradedMessageHandler) {
        window.removeEventListener('message', window._edvinityAutoGradedMessageHandler);
        window._edvinityAutoGradedMessageHandler = null;
        console.log('Auto-graded: Removed message event listener');
      }

      // Handle iframes
      handleIframesForAutoGraded(false);

      // Log that auto-graded has been stopped
      console.log('Auto-graded: Stopped');
    }
  };

  // Function to check if we're on an assessment page
  const checkForAssessmentPage = (doc = document) => {
    try {
      console.log('Auto-graded: Checking for assessment page');

      // Check if we've already processed this page recently
      if (doc.hasAttribute && doc.hasAttribute('data-edvinity-auto-graded-processed')) {
        console.log('Auto-graded: Assessment page already processed, skipping');
        return;
      }

      // Look for the activity title element
      const activityTitle = doc.querySelector('h2#activity-title');
      if (activityTitle) {
        const titleText = activityTitle.textContent.trim().toLowerCase();
        console.log(`Auto-graded: Found activity title: "${titleText}"`);

        // Check if this is a Pre-Test or Quiz & Lecture activity
        if (titleText.includes('pre-test') || titleText.includes('quiz') || titleText.includes('lecture')) {
          console.log(`Auto-graded: Detected assessment page: ${titleText}`);

          // Mark this document as processed
          if (doc.setAttribute) {
            doc.setAttribute('data-edvinity-auto-graded-processed', 'true');
          }

          // Process the assessment page
          processAssessmentPage(doc);

          // Set a timeout to allow for page changes and then check again
          setTimeout(() => {
            if (doc.removeAttribute) {
              doc.removeAttribute('data-edvinity-auto-graded-processed');
            }
          }, 10000); // Increased timeout to 10 seconds

          return;
        }
      }

      // Try to find any elements that might indicate we're on an assessment page
      const navBtnList = doc.querySelector('ol#navBtnList');
      if (navBtnList) {
        console.log('Auto-graded: Found navBtnList element');

        // Check if it has question ID list items
        const listItems = navBtnList.querySelectorAll('li');
        let hasQuestionIds = false;

        for (const item of listItems) {
          if (item.id && item.id !== 'leftArrowBtn' && item.id !== 'rightArrowBtn') {
            // Check if the ID looks like a UUID
            if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(item.id)) {
              hasQuestionIds = true;
              console.log(`Auto-graded: Found question ID in navBtnList: ${item.id}`);
              break;
            }
          }
        }

        if (hasQuestionIds) {
          console.log('Auto-graded: Detected assessment page from navBtnList');

          // Mark this document as processed
          if (doc.setAttribute) {
            doc.setAttribute('data-edvinity-auto-graded-processed', 'true');
          }

          // Process the assessment page
          processAssessmentPage(doc);

          // Set a timeout to allow for page changes and then check again
          setTimeout(() => {
            if (doc.removeAttribute) {
              doc.removeAttribute('data-edvinity-auto-graded-processed');
            }
          }, 10000); // Increased timeout to 10 seconds

          return;
        }
      }

      // Try other assessment indicators
      const possibleAssessmentIndicators = [
        doc.querySelector('div[id^="q_"]'),
        doc.querySelector('.Assessment_Main_Body_Content_Question'),
        doc.querySelector('ol.prog-btn-list'),
        doc.querySelector('form[action*="AssessmentViewer"]')
      ];

      if (possibleAssessmentIndicators.some(el => el !== null)) {
        console.log('Auto-graded: Found potential assessment page indicators');

        // Mark this document as processed
        if (doc.setAttribute) {
          doc.setAttribute('data-edvinity-auto-graded-processed', 'true');
        }

        // Process the assessment page
        processAssessmentPage(doc);

        // Set a timeout to allow for page changes and then check again
        setTimeout(() => {
          if (doc.removeAttribute) {
            doc.removeAttribute('data-edvinity-auto-graded-processed');
          }
        }, 10000); // Increased timeout to 10 seconds
      }
    } catch (e) {
      console.warn('Auto-graded: Error checking for assessment page:', e);
    }
  };

  // Function to process an assessment page
  const processAssessmentPage = (doc) => {
    try {
      console.log('Auto-graded: Processing assessment page');

      // Look for the navigation button list
      const navBtnList = doc.querySelector('ol#navBtnList');
      if (navBtnList) {
        console.log('Auto-graded: Found navigation button list');

        // Get all question IDs from the navigation buttons
        const questionIds = [];
        const navButtons = navBtnList.querySelectorAll('li');

        console.log(`Auto-graded: Found ${navButtons.length} navigation buttons`);

        // Log all button IDs for debugging
        navButtons.forEach(button => {
          console.log(`Auto-graded: Navigation button ID: ${button.id}`);
        });

        navButtons.forEach(button => {
          // Extract the question ID from the button's id attribute
          // Skip the left and right arrow buttons
          const buttonId = button.id;
          if (buttonId && buttonId !== 'leftArrowBtn' && buttonId !== 'rightArrowBtn') {
            questionIds.push(buttonId);

            // Also look for question containers to verify they exist
            const questionContainer = doc.querySelector(`div#q_${buttonId}`);
            if (questionContainer) {
              console.log(`Auto-graded: Found question container for ID: ${buttonId}`);
            } else {
              console.warn(`Auto-graded: Question container not found for ID: ${buttonId}`);

              // Try to find the question container with a different pattern
              const altQuestionContainer = doc.querySelector(`div[id*="${buttonId}"]`);
              if (altQuestionContainer) {
                console.log(`Auto-graded: Found alternative question container for ID: ${buttonId}`);
              }
            }
          }
        });

        console.log(`Auto-graded: Found ${questionIds.length} question IDs`);

        // Process each question
        if (questionIds.length > 0) {
          // Process questions one by one with a delay to avoid overwhelming the API
          processQuestionsSequentially(questionIds, doc);
        } else {
          console.warn('Auto-graded: No question IDs found from navigation buttons');

          // Try an alternative approach - look for question containers directly
          const questionContainers = doc.querySelectorAll('div[id^="q_"]');
          if (questionContainers.length > 0) {
            console.log(`Auto-graded: Found ${questionContainers.length} question containers directly`);

            const alternativeQuestionIds = [];
            questionContainers.forEach(container => {
              // Extract the question ID from the container's id attribute
              const containerId = container.id;
              if (containerId && containerId.startsWith('q_')) {
                const questionId = containerId.substring(2); // Remove the 'q_' prefix
                alternativeQuestionIds.push(questionId);
              }
            });

            console.log(`Auto-graded: Found ${alternativeQuestionIds.length} alternative question IDs`);

            if (alternativeQuestionIds.length > 0) {
              // Process questions one by one with a delay to avoid overwhelming the API
              processQuestionsSequentially(alternativeQuestionIds, doc);
            }
          }
        }
      } else {
        console.warn('Auto-graded: Navigation button list not found');

        // Try to find the navigation button list with a different approach
        const possibleNavLists = doc.querySelectorAll('ol.prog-btn-list, ol.inline');
        if (possibleNavLists.length > 0) {
          console.log(`Auto-graded: Found ${possibleNavLists.length} possible navigation lists`);

          // Try the first one
          const altNavList = possibleNavLists[0];
          const navButtons = altNavList.querySelectorAll('li');

          console.log(`Auto-graded: Found ${navButtons.length} navigation buttons in alternative list`);

          const questionIds = [];
          navButtons.forEach(button => {
            // Extract the question ID from the button's id attribute
            // Skip the left and right arrow buttons
            const buttonId = button.id;
            if (buttonId && buttonId !== 'leftArrowBtn' && buttonId !== 'rightArrowBtn') {
              questionIds.push(buttonId);
              console.log(`Auto-graded: Found question ID from alternative list: ${buttonId}`);
            }
          });

          if (questionIds.length > 0) {
            // Process questions one by one with a delay to avoid overwhelming the API
            processQuestionsSequentially(questionIds, doc);
            return;
          }
        }

        // Try an alternative approach - look for question containers directly
        const questionContainers = doc.querySelectorAll('div[id^="q_"]');
        if (questionContainers.length > 0) {
          console.log(`Auto-graded: Found ${questionContainers.length} question containers directly`);

          const alternativeQuestionIds = [];
          questionContainers.forEach(container => {
            // Extract the question ID from the container's id attribute
            const containerId = container.id;
            if (containerId && containerId.startsWith('q_')) {
              const questionId = containerId.substring(2); // Remove the 'q_' prefix
              alternativeQuestionIds.push(questionId);
            }
          });

          console.log(`Auto-graded: Found ${alternativeQuestionIds.length} alternative question IDs`);

          if (alternativeQuestionIds.length > 0) {
            // Process questions one by one with a delay to avoid overwhelming the API
            processQuestionsSequentially(alternativeQuestionIds, doc);
          }
        } else {
          console.warn('Auto-graded: No question containers found either');

          // Last resort - try to find any elements that might contain question IDs
          findQuestionIdsByOtherMeans(doc);
        }
      }
    } catch (e) {
      console.warn('Auto-graded: Error processing assessment page:', e);
    }
  };

  // Function to find question IDs by other means
  const findQuestionIdsByOtherMeans = (doc) => {
    try {
      console.log('Auto-graded: Looking for question IDs by other means');

      // Try to find question IDs in various attributes
      const possibleIdContainers = [
        ...doc.querySelectorAll('[id]'),
        ...doc.querySelectorAll('[data-id]'),
        ...doc.querySelectorAll('[data-question-id]'),
        ...doc.querySelectorAll('[name]'),
        ...doc.querySelectorAll('input'),
        ...doc.querySelectorAll('form'),
        ...doc.querySelectorAll('[class*="question"]'),
        ...doc.querySelectorAll('[class*="Question"]'),
        ...doc.querySelectorAll('[class*="assessment"]'),
        ...doc.querySelectorAll('[class*="Assessment"]')
      ];

      console.log(`Auto-graded: Found ${possibleIdContainers.length} elements that might contain question IDs`);

      // Look for UUIDs in attributes
      const uuidRegex = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i;
      // Also look for numeric IDs that might be question IDs
      const numericIdRegex = /^[0-9]{5,}$/;
      const foundIds = new Set();

      possibleIdContainers.forEach(element => {
        // Check id attribute
        if (element.id) {
          if (uuidRegex.test(element.id)) {
            foundIds.add(element.id);
            console.log(`Auto-graded: Found UUID in id attribute: ${element.id}`);
          } else if (numericIdRegex.test(element.id)) {
            foundIds.add(element.id);
            console.log(`Auto-graded: Found numeric ID in id attribute: ${element.id}`);
          } else if (element.id.startsWith('q_') && element.id.length > 2) {
            // Extract the ID after the q_ prefix
            const extractedId = element.id.substring(2);
            foundIds.add(extractedId);
            console.log(`Auto-graded: Found ID with q_ prefix: ${extractedId}`);
          }
        }

        // Check data-id attribute
        if (element.dataset && element.dataset.id) {
          if (uuidRegex.test(element.dataset.id)) {
            foundIds.add(element.dataset.id);
            console.log(`Auto-graded: Found UUID in data-id attribute: ${element.dataset.id}`);
          } else if (numericIdRegex.test(element.dataset.id)) {
            foundIds.add(element.dataset.id);
            console.log(`Auto-graded: Found numeric ID in data-id attribute: ${element.dataset.id}`);
          }
        }

        // Check data-question-id attribute
        if (element.dataset && element.dataset.questionId) {
          if (uuidRegex.test(element.dataset.questionId)) {
            foundIds.add(element.dataset.questionId);
            console.log(`Auto-graded: Found UUID in data-question-id attribute: ${element.dataset.questionId}`);
          } else if (numericIdRegex.test(element.dataset.questionId)) {
            foundIds.add(element.dataset.questionId);
            console.log(`Auto-graded: Found numeric ID in data-question-id attribute: ${element.dataset.questionId}`);
          }
        }

        // Check name attribute
        if (element.name) {
          if (uuidRegex.test(element.name)) {
            foundIds.add(element.name);
            console.log(`Auto-graded: Found UUID in name attribute: ${element.name}`);
          } else if (numericIdRegex.test(element.name)) {
            foundIds.add(element.name);
            console.log(`Auto-graded: Found numeric ID in name attribute: ${element.name}`);
          }
        }

        // Check value attribute
        if (element.value) {
          if (uuidRegex.test(element.value)) {
            foundIds.add(element.value);
            console.log(`Auto-graded: Found UUID in value attribute: ${element.value}`);
          } else if (numericIdRegex.test(element.value)) {
            foundIds.add(element.value);
            console.log(`Auto-graded: Found numeric ID in value attribute: ${element.value}`);
          }
        }
      });

      const questionIds = Array.from(foundIds);
      console.log(`Auto-graded: Found ${questionIds.length} potential question IDs by various patterns`);

      if (questionIds.length > 0) {
        // Process questions one by one with a delay to avoid overwhelming the API
        processQuestionsSequentially(questionIds, doc);
      } else {
        console.warn('Auto-graded: Could not find any potential question IDs');

        // Last resort: try to find any element with a class or ID that might be related to questions
        const possibleQuestionElements = doc.querySelectorAll('[class*="question"], [id*="question"], [class*="Question"], [id*="Question"]');
        console.log(`Auto-graded: Last resort - found ${possibleQuestionElements.length} elements that might be related to questions`);

        if (possibleQuestionElements.length > 0) {
          // Just use the first 5 elements as a test
          const testIds = [];
          for (let i = 0; i < Math.min(possibleQuestionElements.length, 5); i++) {
            const element = possibleQuestionElements[i];
            const testId = `question_${i + 1}`;
            testIds.push(testId);
            console.log(`Auto-graded: Using test ID ${testId} for element:`, element);
          }

          // Process these test IDs
          processQuestionsSequentially(testIds, doc);
        }
      }
    } catch (e) {
      console.warn('Auto-graded: Error finding question IDs by other means:', e);
    }
  };

  // Function to process questions sequentially with a delay
  const processQuestionsSequentially = (questionIds, doc, index = 0) => {
    if (index >= questionIds.length) {
      console.log('Auto-graded: Finished processing all questions');
      return;
    }

    // Process the current question
    const questionId = questionIds[index];
    console.log(`Auto-graded: Processing question ${index + 1}/${questionIds.length}: ${questionId}`);

    // Fetch and apply the answer for this question
    fetchAndApplyAnswer(questionId, doc)
      .then(() => {
        // Process the next question after a delay
        console.log(`Auto-graded: Scheduling next question (${index + 2}/${questionIds.length}) after 1 second delay`);
        setTimeout(() => {
          processQuestionsSequentially(questionIds, doc, index + 1);
        }, 1000); // 1 second delay between questions
      })
      .catch(error => {
        console.warn(`Auto-graded: Error processing question: ${error.message}`);
        // Continue with the next question despite the error
        console.log(`Auto-graded: Continuing with next question despite error`);
        setTimeout(() => {
          processQuestionsSequentially(questionIds, doc, index + 1);
        }, 1000);
      });
  };

  // Function to fetch the answer for a question and apply it
  const fetchAndApplyAnswer = (questionId, doc) => {
    return new Promise((resolve, reject) => {
      try {
        // Log the question ID we're fetching
        console.log(`Auto-graded: Fetching answer for question ID: ${questionId}`);

        // Create the API URL - ensure the questionId is properly encoded
        const encodedQuestionId = encodeURIComponent(questionId);
        const apiUrl = `http://localhost:3000/api/answer/${encodedQuestionId}`;
        console.log(`Auto-graded: API URL: ${apiUrl}`);

        // Add detailed logging for debugging
        console.log(`Auto-graded: Making fetch request to ${apiUrl}`);

        // Create a fetch request to the API with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        fetch(apiUrl, {
          signal: controller.signal,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        })
          .then(response => {
            clearTimeout(timeoutId);
            console.log(`Auto-graded: API response status: ${response.status}`);

            if (!response.ok) {
              throw new Error(`API request failed with status ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            // Log the full response for debugging
            console.log(`Auto-graded: Received API response for question ${questionId}:`, JSON.stringify(data));

            // Check if we have valid data
            if (data && data.data && data.data.answer_ids) {
              console.log(`Auto-graded: Valid answer data found with answer_ids:`, data.data.answer_ids);
              // Apply the answer to the question
              applyAnswerToQuestion(questionId, data.data, doc);
              resolve();
            } else if (data && data.success === true && data.data) {
              // Alternative data structure
              console.log(`Auto-graded: Alternative data structure detected:`, data.data);
              applyAnswerToQuestion(questionId, data.data, doc);
              resolve();
            } else if (data && data.data && data.data.ids) {
              // Another alternative structure
              console.log(`Auto-graded: Alternative data structure with ids detected:`, data.data.ids);
              applyAnswerToQuestion(questionId, data.data, doc);
              resolve();
            } else if (data && data.success === true) {
              // Success but no data
              console.warn(`Auto-graded: API returned success but no usable data`);
              reject(new Error('API returned success but no usable data'));
            } else {
              console.warn(`Auto-graded: Invalid answer data format for question ${questionId}:`, data);

              // Try to use any data we can find in the response
              if (data) {
                console.log(`Auto-graded: Attempting to use any available data in the response`);
                applyAnswerToQuestion(questionId, data, doc);
                resolve();
              } else {
                reject(new Error('Invalid answer data format'));
              }
            }
          })
          .catch(error => {
            clearTimeout(timeoutId);
            console.warn(`Auto-graded: Error fetching answer: ${error.message}`);

            // Try an alternative approach - direct API call with XMLHttpRequest
            console.log(`Auto-graded: Trying alternative API call with XMLHttpRequest`);
            tryAlternativeApiCall(questionId, doc, resolve, reject);
          });
      } catch (e) {
        console.warn(`Auto-graded: Error in fetchAndApplyAnswer: ${e.message}`);
        reject(e);
      }
    });
  };


  // Function to try an alternative API call
  const tryAlternativeApiCall = (questionId, doc, resolve, reject) => {
    try {
      console.log(`Auto-graded: Making alternative API call for question ID: ${questionId}`);

      // Create the API URL - ensure the questionId is properly encoded
      const encodedQuestionId = encodeURIComponent(questionId);
      const apiUrl = `http://localhost:3000/api/answer/${encodedQuestionId}`;
      console.log(`Auto-graded: Alternative API URL: ${apiUrl}`);

      // Create an XMLHttpRequest with timeout
      const xhr = new XMLHttpRequest();
      xhr.open('GET', apiUrl, true);
      xhr.timeout = 5000; // 5 second timeout

      // Set headers
      xhr.setRequestHeader('Accept', 'application/json');
      xhr.setRequestHeader('Content-Type', 'application/json');

      xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
          console.log(`Auto-graded: Alternative API call successful with status: ${xhr.status}`);

          try {
            const data = JSON.parse(xhr.responseText);
            console.log(`Auto-graded: Parsed response from alternative API call:`, xhr.responseText);

            // Check if we have valid data
            if (data && data.data && data.data.answer_ids) {
              console.log(`Auto-graded: Valid answer data found with answer_ids from alternative API call:`, data.data.answer_ids);
              // Apply the answer to the question
              applyAnswerToQuestion(questionId, data.data, doc);
              resolve();
            } else if (data && data.success === true && data.data) {
              // Alternative data structure
              console.log(`Auto-graded: Alternative data structure detected from alternative API call:`, data.data);
              applyAnswerToQuestion(questionId, data.data, doc);
              resolve();
            } else if (data && data.data && data.data.ids) {
              // Another alternative structure
              console.log(`Auto-graded: Alternative data structure with ids detected from alternative API call:`, data.data.ids);
              applyAnswerToQuestion(questionId, data.data, doc);
              resolve();
            } else {
              console.warn(`Auto-graded: Invalid answer data format from alternative API call:`, data);

              // Try to use any data we can find in the response
              if (data) {
                console.log(`Auto-graded: Attempting to use any available data in the alternative API response`);
                applyAnswerToQuestion(questionId, data, doc);
                resolve();
              } else {
                reject(new Error('Invalid answer data format from alternative API call'));
              }
            }
          } catch (e) {
            console.warn(`Auto-graded: Error parsing response from alternative API call: ${e.message}`);
            reject(e);
          }
        } else {
          console.warn(`Auto-graded: Alternative API call failed with status: ${xhr.status}`);
          reject(new Error(`Alternative API call failed with status: ${xhr.status}`));
        }
      };

      xhr.ontimeout = function() {
        console.warn(`Auto-graded: Alternative API call timed out`);
        reject(new Error('Alternative API call timed out'));
      };

      xhr.onerror = function() {
        console.warn(`Auto-graded: Network error during alternative API call`);
        reject(new Error('Network error during alternative API call'));
      };

      xhr.send();
    } catch (e) {
      console.warn(`Auto-graded: Error in alternative API call: ${e.message}`);
      reject(e);
    }
  };

  // Function to apply an answer to a question
  const applyAnswerToQuestion = (questionId, answerData, doc) => {
    try {
      console.log(`Auto-graded: Applying answer to question: ${questionId}`);

      // Find the question container - try multiple patterns
      let questionContainer = null;

      // Try with q_ prefix
      questionContainer = doc.querySelector(`div#q_${questionId}`);
      if (questionContainer) {
        console.log(`Auto-graded: Found question container with q_ prefix`);
      }

      // Try with the ID directly
      if (!questionContainer) {
        questionContainer = doc.querySelector(`div#${questionId}`);
        if (questionContainer) {
          console.log(`Auto-graded: Found question container with direct ID`);
        }
      }

      // Try with ID as part of another ID
      if (!questionContainer) {
        const containers = doc.querySelectorAll(`div[id*="${questionId}"]`);
        if (containers.length > 0) {
          questionContainer = containers[0];
          console.log(`Auto-graded: Found question container with ID as part of another ID`);
        }
      }

      // Try with other patterns
      if (!questionContainer) {
        console.warn(`Auto-graded: Question container not found with standard patterns`);

        // Try to find the question container by other means
        console.log(`Auto-graded: Trying to find question container by other means`);
        questionContainer = findQuestionContainerByOtherMeans(questionId, doc);

        if (!questionContainer) {
          console.warn(`Auto-graded: Could not find question container by any means`);

          // Last resort: try to find any container that might be the question
          const possibleContainers = [
            doc.querySelector('.Assessment_Main_Body_Content_Question'),
            doc.querySelector('.question-container'),
            doc.querySelector('form'),
            doc.querySelector('body') // Fallback to body if nothing else works
          ];

          for (const container of possibleContainers) {
            if (container) {
              questionContainer = container;
              console.log(`Auto-graded: Using fallback container`);
              break;
            }
          }

          if (!questionContainer) {
            console.warn(`Auto-graded: No suitable container found, aborting`);
            return;
          }
        } else {
          console.log(`Auto-graded: Found alternative question container`);
        }
      }

      // Check if we have answer data and extract answer_ids
      let answerIds = [];

      // Try to extract answer IDs from various possible data structures
      if (answerData.answer_ids && Array.isArray(answerData.answer_ids) && answerData.answer_ids.length > 0) {
        // Standard format
        answerIds = answerData.answer_ids;
        console.log(`Auto-graded: Using standard answer_ids format: ${JSON.stringify(answerIds)}`);
      } else if (answerData.data && answerData.data.answer_ids && Array.isArray(answerData.data.answer_ids) && answerData.data.answer_ids.length > 0) {
        // Nested data structure
        answerIds = answerData.data.answer_ids;
        console.log(`Auto-graded: Using nested data.answer_ids format: ${JSON.stringify(answerIds)}`);
      } else if (answerData.ids && Array.isArray(answerData.ids) && answerData.ids.length > 0) {
        // Alternative format
        answerIds = answerData.ids;
        console.log(`Auto-graded: Using alternative ids format: ${JSON.stringify(answerIds)}`);
      } else if (answerData.data && answerData.data.ids && Array.isArray(answerData.data.ids) && answerData.data.ids.length > 0) {
        // Nested alternative format
        answerIds = answerData.data.ids;
        console.log(`Auto-graded: Using nested data.ids format: ${JSON.stringify(answerIds)}`);
      } else if (answerData.id) {
        // Single answer format
        answerIds = [answerData.id];
        console.log(`Auto-graded: Using single id format: ${answerData.id}`);
      } else if (answerData.data && answerData.data.id) {
        // Nested single answer format
        answerIds = [answerData.data.id];
        console.log(`Auto-graded: Using nested data.id format: ${answerData.data.id}`);
      } else if (answerData.answer) {
        // Text answer format
        const answerText = answerData.answer;
        console.log(`Auto-graded: Using text answer format: ${answerText}`);

        // Process text answer
        processTextAnswer(questionContainer, answerText, answerIds);
      } else if (answerData.data && answerData.data.answer) {
        // Nested text answer format
        const answerText = answerData.data.answer;
        console.log(`Auto-graded: Using nested data.answer format: ${answerText}`);

        // Process text answer
        processTextAnswer(questionContainer, answerText, answerIds);
      } else {
        // Try to find any property that might contain answer data
        console.log(`Auto-graded: No standard answer format found, looking for any property that might contain answer data`);

        // Look for properties that might contain answer data
        for (const key in answerData) {
          if (key.toLowerCase().includes('answer') || key.toLowerCase().includes('id')) {
            console.log(`Auto-graded: Found potential answer data in property: ${key}`);

            const value = answerData[key];
            if (Array.isArray(value) && value.length > 0) {
              // Array of values
              answerIds = value;
              console.log(`Auto-graded: Using array from property ${key}: ${JSON.stringify(value)}`);
              break;
            } else if (typeof value === 'string' || typeof value === 'number') {
              // Single value
              answerIds = [value.toString()];
              console.log(`Auto-graded: Using single value from property ${key}: ${value}`);
              break;
            } else if (typeof value === 'object' && value !== null) {
              // Object - look for nested properties
              for (const nestedKey in value) {
                if (nestedKey.toLowerCase().includes('answer') || nestedKey.toLowerCase().includes('id')) {
                  const nestedValue = value[nestedKey];
                  if (Array.isArray(nestedValue) && nestedValue.length > 0) {
                    // Array of values
                    answerIds = nestedValue;
                    console.log(`Auto-graded: Using array from nested property ${key}.${nestedKey}: ${JSON.stringify(nestedValue)}`);
                    break;
                  } else if (typeof nestedValue === 'string' || typeof nestedValue === 'number') {
                    // Single value
                    answerIds = [nestedValue.toString()];
                    console.log(`Auto-graded: Using single value from nested property ${key}.${nestedKey}: ${nestedValue}`);
                    break;
                  }
                }
              }

              if (answerIds.length > 0) {
                break;
              }
            }
          }
        }
      }

      // If we still don't have any answer IDs, try to find any inputs as a fallback
      if (answerIds.length === 0) {
        console.warn(`Auto-graded: Could not find any answer IDs, using fallback approach`);

        // Try to find any inputs as a fallback
        const allInputs = questionContainer.querySelectorAll('input[type="radio"], input[type="checkbox"]');
        if (allInputs.length > 0) {
          // Just use the first input as a fallback
          const firstInput = allInputs[0];
          answerIds.push(firstInput.value || firstInput.id);
          console.log(`Auto-graded: Using first available input as fallback`);
        } else {
          console.warn(`Auto-graded: No inputs found, aborting`);
          return;
        }
      }

      console.log(`Auto-graded: Applying answers for question ${questionId} with answer IDs: ${answerIds.join(', ')}`);

      // Make the question visible if it's not already
      if (questionContainer.style.display === 'none') {
        // Find the corresponding navigation button and click it
        const navButton = doc.querySelector(`li#${questionId} a`);
        if (navButton) {
          console.log(`Auto-graded: Clicking navigation button to make question visible`);
          navButton.click();
        } else {
          console.warn(`Auto-graded: Navigation button not found for question: ${questionId}`);
        }
      }

      // Find and select the correct answer(s)
      let foundAnyAnswer = false;

      answerIds.forEach(answerId => {
        console.log(`Auto-graded: Looking for input with answer ID: ${answerId}`);

        // Look for the input element with the matching answer ID
        // Try different selector patterns since the HTML structure might vary
        let inputElement = null;

        // Try with value attribute
        inputElement = questionContainer.querySelector(`input[value="${answerId}"]`);
        if (inputElement) {
          console.log(`Auto-graded: Found input by value attribute`);
        }

        // If not found, try with ID selector
        if (!inputElement) {
          inputElement = questionContainer.querySelector(`#${answerId}`);
          if (inputElement) {
            console.log(`Auto-graded: Found input by ID selector`);
          }
        }

        // If not found, try with name and ID combination
        if (!inputElement) {
          try {
            const questionName = `${questionId}_${answerId.split('_').slice(1).join('_')}`;
            inputElement = questionContainer.querySelector(`input[name="${questionName}"][id="${answerId}"]`);
            if (inputElement) {
              console.log(`Auto-graded: Found input by name and ID combination`);
            }
          } catch (e) {
            console.warn(`Auto-graded: Error parsing answer ID: ${e.message}`);
          }
        }

        // If still not found, try a more general approach
        if (!inputElement) {
          // Try to find any input that contains the answer ID in its attributes
          const allInputs = questionContainer.querySelectorAll('input[type="radio"], input[type="checkbox"]');
          console.log(`Auto-graded: Searching through ${allInputs.length} input elements`);

          for (const input of allInputs) {
            if (input.id === answerId ||
                input.value === answerId ||
                input.id.includes(answerId) ||
                input.value.includes(answerId)) {
              inputElement = input;
              console.log(`Auto-graded: Found input by partial match`);
              break;
            }
          }
        }

        if (inputElement) {
          // Check the input element
          inputElement.checked = true;
          foundAnyAnswer = true;

          // Trigger a change event to ensure the UI updates
          const event = new Event('change', { bubbles: true });
          inputElement.dispatchEvent(event);

          // Also trigger a click event for good measure
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
          });
          inputElement.dispatchEvent(clickEvent);

          console.log(`Auto-graded: Selected answer for question ${questionId}`);
        } else {
          console.warn(`Auto-graded: Answer input not found for ID: ${answerId}`);

          // Last resort: try to find any input that might be the correct answer
          const allInputs = questionContainer.querySelectorAll('input[type="radio"], input[type="checkbox"]');
          if (allInputs.length > 0) {
            console.log(`Auto-graded: Attempting last resort: selecting first available input`);

            // Just select the first input as a fallback
            const firstInput = allInputs[0];
            firstInput.checked = true;

            // Trigger events
            const event = new Event('change', { bubbles: true });
            firstInput.dispatchEvent(event);

            const clickEvent = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window
            });
            firstInput.dispatchEvent(clickEvent);

            console.log(`Auto-graded: Selected first available input as fallback`);
            foundAnyAnswer = true;
          }
        }
      });

      if (!foundAnyAnswer) {
        console.warn(`Auto-graded: Could not find any matching answer inputs for question: ${questionId}`);
      }
    } catch (e) {
      console.warn('Auto-graded: Error applying answer to question:', e);
    }
  };

  // Function to process text answers
  const processTextAnswer = (questionContainer, answerText, answerIds) => {
    try {
      console.log(`Auto-graded: Processing text answer: ${answerText}`);

      // Look for labels that contain this text
      const labels = questionContainer.querySelectorAll('label');
      console.log(`Auto-graded: Found ${labels.length} labels to check for text match`);

      for (const label of labels) {
        if (label.textContent.includes(answerText)) {
          console.log(`Auto-graded: Found label with matching text: "${label.textContent}"`);

          // Find the associated input
          let input = null;

          // Try different approaches to find the associated input

          // 1. Check if the input is the previous sibling
          if (label.previousElementSibling &&
              (label.previousElementSibling.type === 'radio' ||
               label.previousElementSibling.type === 'checkbox')) {
            input = label.previousElementSibling;
            console.log(`Auto-graded: Found input as previous sibling`);
          }

          // 2. Check if the input is inside the label
          if (!input) {
            const nestedInput = label.querySelector('input[type="radio"], input[type="checkbox"]');
            if (nestedInput) {
              input = nestedInput;
              console.log(`Auto-graded: Found input nested inside label`);
            }
          }

          // 3. Check if the label has a for attribute
          if (!input && label.htmlFor) {
            const linkedInput = questionContainer.querySelector(`#${label.htmlFor}`);
            if (linkedInput && (linkedInput.type === 'radio' || linkedInput.type === 'checkbox')) {
              input = linkedInput;
              console.log(`Auto-graded: Found input via label's for attribute`);
            }
          }

          // If we found an input, add its ID or value to the answer IDs
          if (input) {
            answerIds.push(input.value || input.id);
            console.log(`Auto-graded: Added input with value/id: ${input.value || input.id}`);
          }
        }
      }

      // If we didn't find any matching labels, try a more flexible approach
      if (answerIds.length === 0) {
        console.log(`Auto-graded: No exact label matches found, trying partial matching`);

        // Split the answer text into words for partial matching
        const answerWords = answerText.toLowerCase().split(/\s+/).filter(word => word.length > 3);
        console.log(`Auto-graded: Looking for these keywords: ${answerWords.join(', ')}`);

        // Look for labels that contain any of the significant words from the answer
        for (const label of labels) {
          const labelText = label.textContent.toLowerCase();
          const matchingWords = answerWords.filter(word => labelText.includes(word));

          if (matchingWords.length > 0) {
            console.log(`Auto-graded: Found label with partial match: "${label.textContent}" (matched words: ${matchingWords.join(', ')})`);

            // Find the associated input using the same approaches as above
            let input = null;

            if (label.previousElementSibling &&
                (label.previousElementSibling.type === 'radio' ||
                 label.previousElementSibling.type === 'checkbox')) {
              input = label.previousElementSibling;
            } else if (label.querySelector('input[type="radio"], input[type="checkbox"]')) {
              input = label.querySelector('input[type="radio"], input[type="checkbox"]');
            } else if (label.htmlFor) {
              const linkedInput = questionContainer.querySelector(`#${label.htmlFor}`);
              if (linkedInput && (linkedInput.type === 'radio' || linkedInput.type === 'checkbox')) {
                input = linkedInput;
              }
            }

            if (input) {
              answerIds.push(input.value || input.id);
              console.log(`Auto-graded: Added input with value/id: ${input.value || input.id}`);
            }
          }
        }
      }

      console.log(`Auto-graded: Processed text answer, found ${answerIds.length} matching inputs`);
    } catch (e) {
      console.warn(`Auto-graded: Error processing text answer: ${e.message}`);
    }
  };

  // Function to find a question container by other means
  const findQuestionContainerByOtherMeans = (questionId, doc) => {
    try {
      console.log(`Auto-graded: Looking for question container by other means for ID: ${questionId}`);

      // Try different selector patterns
      const selectors = [
        `div[id*="${questionId}"]`,
        `form[id*="${questionId}"]`,
        `div[data-id*="${questionId}"]`,
        `div[data-question-id*="${questionId}"]`,
        `div.Assessment_Main_Body_Content_Question`,
        `div.question-container`,
        `[class*="question"]`,
        `[class*="Question"]`,
        `[class*="assessment"]`,
        `[class*="Assessment"]`
      ];

      for (const selector of selectors) {
        const elements = doc.querySelectorAll(selector);
        if (elements.length > 0) {
          console.log(`Auto-graded: Found potential containers using selector: ${selector}`);
          return elements[0]; // Return the first matching element
        }
      }

      console.warn(`Auto-graded: Could not find question container by any means`);
      return null;
    } catch (e) {
      console.warn(`Auto-graded: Error finding question container by other means: ${e.message}`);
      return null;
    }
  };

  // Function to handle iframes for auto-graded
  const handleIframesForAutoGraded = (enable) => {
    // Function to process an iframe
    const processIframe = (iframe) => {
      try {
        // Check if we can access the iframe's content document
        if (iframe.contentDocument) {
          if (enable) {
            console.log('Auto-graded: Processing iframe', iframe);

            // Add a data attribute to mark this iframe as processed
            iframe.setAttribute('data-edvinity-auto-graded-iframe', 'true');

            // Inject a script into the iframe to help with communication
            try {
              // Create a script element to inject into the iframe
              const script = iframe.contentDocument.createElement('script');
              script.textContent = `
                // Create a function to send question IDs to the parent window
                window.sendQuestionIdsToParent = function(questionIds) {
                  if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                      type: 'edvinity-auto-graded-question-ids',
                      questionIds: questionIds
                    }, '*');
                    console.log('Auto-graded: Sent question IDs to parent window:', questionIds);
                  }
                };

                // Create a function to find question IDs in the iframe
                window.findQuestionIdsInIframe = function() {
                  console.log('Auto-graded: Looking for question IDs in iframe');

                  // Look for navigation buttons
                  const navBtnList = document.querySelector('.navBtnList');
                  if (navBtnList) {
                    const navButtons = navBtnList.querySelectorAll('li');
                    const questionIds = [];

                    navButtons.forEach(button => {
                      const buttonId = button.id;
                      if (buttonId && buttonId !== 'leftArrowBtn' && buttonId !== 'rightArrowBtn') {
                        questionIds.push(buttonId);
                      }
                    });

                    if (questionIds.length > 0) {
                      console.log('Auto-graded: Found question IDs in iframe:', questionIds);
                      window.sendQuestionIdsToParent(questionIds);
                      return;
                    }
                  }

                  // Look for question containers
                  const questionContainers = document.querySelectorAll('div[id^="q_"]');
                  if (questionContainers.length > 0) {
                    const questionIds = [];

                    questionContainers.forEach(container => {
                      const containerId = container.id;
                      if (containerId && containerId.startsWith('q_')) {
                        const questionId = containerId.substring(2);
                        questionIds.push(questionId);
                      }
                    });

                    if (questionIds.length > 0) {
                      console.log('Auto-graded: Found question IDs in iframe from containers:', questionIds);
                      window.sendQuestionIdsToParent(questionIds);
                      return;
                    }
                  }

                  // Look for any elements with IDs that might be question IDs
                  const allElements = document.querySelectorAll('[id]');
                  const potentialIds = [];

                  allElements.forEach(element => {
                    if (element.id && (element.id.match(/^[0-9]{5,}$/) || element.id.match(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i))) {
                      potentialIds.push(element.id);
                    }
                  });

                  if (potentialIds.length > 0) {
                    console.log('Auto-graded: Found potential question IDs in iframe:', potentialIds);
                    window.sendQuestionIdsToParent(potentialIds);
                  }
                };

                // Run the function immediately
                window.findQuestionIdsInIframe();

                // Set up a MutationObserver to detect changes in the iframe
                const observer = new MutationObserver(function(mutations) {
                  // Only run if there were meaningful changes
                  const shouldCheck = mutations.some(mutation => {
                    return mutation.addedNodes.length > 0 ||
                           mutation.removedNodes.length > 0 ||
                           mutation.type === 'attributes';
                  });

                  if (shouldCheck) {
                    window.findQuestionIdsInIframe();
                  }
                });

                // Start observing the document
                observer.observe(document.body, {
                  childList: true,
                  subtree: true,
                  attributes: true
                });

                // Store the observer to prevent garbage collection
                window._edvinityAutoGradedObserver = observer;

                console.log('Auto-graded: Script injected into iframe');
              `;

              // Append the script to the iframe's document
              iframe.contentDocument.head.appendChild(script);
              console.log('Auto-graded: Injected script into iframe');
            } catch (scriptError) {
              console.warn('Auto-graded: Error injecting script into iframe:', scriptError);
            }

            // Check if the iframe contains an assessment page
            checkForAssessmentPage(iframe.contentDocument);

            // Set up an observer for the iframe
            const iframeObserver = new MutationObserver((mutations) => {
              // Only check if there were meaningful changes
              const shouldCheck = mutations.some(mutation => {
                return mutation.addedNodes.length > 0 ||
                       mutation.removedNodes.length > 0 ||
                       mutation.type === 'attributes';
              });

              if (shouldCheck) {
                checkForAssessmentPage(iframe.contentDocument);
              }
            });

            // Start observing the iframe document
            if (iframe.contentDocument.body) {
              iframeObserver.observe(iframe.contentDocument.body, {
                childList: true,
                subtree: true,
                attributes: true
              });

              // Store the observer to prevent garbage collection
              if (!window.edvinityIframeAutoGradedObservers) {
                window.edvinityIframeAutoGradedObservers = [];
              }
              window.edvinityIframeAutoGradedObservers.push({
                observer: iframeObserver,
                iframe: iframe
              });
            }

            // Also observe iframe load events to handle navigation within the iframe
            iframe.addEventListener('load', function iframeLoadHandler() {
              console.log('Auto-graded: Iframe loaded, checking for assessment page');
              setTimeout(() => {
                checkForAssessmentPage(iframe.contentDocument);

                // Try to inject the script again after the iframe has loaded
                try {
                  const script = iframe.contentDocument.createElement('script');
                  script.textContent = `
                    // Create a function to send question IDs to the parent window
                    window.sendQuestionIdsToParent = function(questionIds) {
                      if (window.parent && window.parent !== window) {
                        window.parent.postMessage({
                          type: 'edvinity-auto-graded-question-ids',
                          questionIds: questionIds
                        }, '*');
                        console.log('Auto-graded: Sent question IDs to parent window:', questionIds);
                      }
                    };

                    // Find and send question IDs
                    window.findQuestionIdsInIframe();
                  `;

                  // Append the script to the iframe's document
                  iframe.contentDocument.head.appendChild(script);
                  console.log('Auto-graded: Re-injected script into iframe after load');
                } catch (scriptError) {
                  console.warn('Auto-graded: Error re-injecting script into iframe after load:', scriptError);
                }
              }, 1000);
            });

            // Store the event listener for later removal
            iframe._edvinityLoadHandler = iframe._edvinityLoadHandler || [];
            iframe._edvinityLoadHandler.push(iframeLoadHandler);
          } else {
            // If disabling, disconnect any observers
            if (window.edvinityIframeAutoGradedObservers) {
              window.edvinityIframeAutoGradedObservers.forEach(item => {
                if (item.observer) {
                  item.observer.disconnect();
                }
              });
              window.edvinityIframeAutoGradedObservers = [];
            }

            // Remove load event listeners
            if (iframe._edvinityLoadHandler) {
              iframe._edvinityLoadHandler.forEach(handler => {
                iframe.removeEventListener('load', handler);
              });
              iframe._edvinityLoadHandler = [];
            }

            // Remove the data attribute
            iframe.removeAttribute('data-edvinity-auto-graded-iframe');

            // Try to clean up the script in the iframe
            try {
              if (iframe.contentDocument && iframe.contentDocument._edvinityAutoGradedObserver) {
                iframe.contentDocument._edvinityAutoGradedObserver.disconnect();
                iframe.contentDocument._edvinityAutoGradedObserver = null;
              }
            } catch (e) {
              console.warn('Auto-graded: Error cleaning up iframe observer:', e);
            }
          }
        }
      } catch (e) {
        console.warn('Auto-graded: Could not access iframe content (cross-origin):', e);
      }
    };

    // Process all existing iframes
    document.querySelectorAll('iframe').forEach(processIframe);

    // Set up an observer to detect new iframes
    if (enable) {
      if (!window.edvinityIframeDetectionObserver) {
        window.edvinityIframeDetectionObserver = new MutationObserver((mutations) => {
          mutations.forEach(mutation => {
            if (mutation.addedNodes.length) {
              mutation.addedNodes.forEach(node => {
                // Check if the added node is an iframe
                if (node.tagName === 'IFRAME') {
                  processIframe(node);
                }

                // Check if the added node contains iframes
                if (node.querySelectorAll) {
                  node.querySelectorAll('iframe').forEach(processIframe);
                }
              });
            }
          });
        });

        // Start observing the document for new iframes
        window.edvinityIframeDetectionObserver.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    } else {
      // Disconnect the iframe detection observer
      if (window.edvinityIframeDetectionObserver) {
        window.edvinityIframeDetectionObserver.disconnect();
        window.edvinityIframeDetectionObserver = null;
      }
    }
  };

  // Function to update visibility of spoof mode related fields
  const updateSpoofModeVisibility = (spoofMode) => {
    console.log('Updating spoof mode visibility for mode:', spoofMode);

    // Debug the DOM structure first
    debugDropdownStructure();

    // Try multiple approaches to find the elements
    let presetNamesItem = null;
    let customNameItem = null;

    // First try: Look in the specific dropdown
    const nameSpoofDropdown = document.getElementById('dropdown-name-spoofer');
    console.log('Found name spoof dropdown:', nameSpoofDropdown);

    if (nameSpoofDropdown) {
      presetNamesItem = nameSpoofDropdown.querySelector('[data-item-id="preset-names"]');
      customNameItem = nameSpoofDropdown.querySelector('[data-item-id="custom-name"]');
    }

    // Second try: Search globally if not found in dropdown
    if (!presetNamesItem || !customNameItem) {
      console.log('Items not found in dropdown, searching globally...');
      presetNamesItem = presetNamesItem || document.querySelector('[data-item-id="preset-names"]');
      customNameItem = customNameItem || document.querySelector('[data-item-id="custom-name"]');
    }

    // Third try: Search by ID if still not found
    if (!presetNamesItem || !customNameItem) {
      console.log('Items still not found, trying by ID...');
      presetNamesItem = presetNamesItem || document.getElementById('setting-preset-names');
      customNameItem = customNameItem || document.getElementById('setting-custom-name');
    }

    console.log('Final search results:');
    console.log('- Preset names item:', presetNamesItem);
    console.log('- Custom name item:', customNameItem);

    // Apply visibility settings
    if (presetNamesItem) {
      presetNamesItem.style.display = spoofMode === 'preset' ? 'flex' : 'none';
      console.log('Set preset names visibility to:', spoofMode === 'preset' ? 'flex' : 'none');
    } else {
      console.warn('Could not find preset names item to set visibility');
    }

    if (customNameItem) {
      customNameItem.style.display = spoofMode === 'custom' ? 'flex' : 'none';
      console.log('Set custom name visibility to:', spoofMode === 'custom' ? 'flex' : 'none');
    } else {
      console.warn('Could not find custom name item to set visibility');
    }

    // If we still can't find the items, set up a retry mechanism
    if (!presetNamesItem || !customNameItem) {
      console.log('Setting up retry mechanism for visibility update...');
      setTimeout(() => {
        console.log('Retrying visibility update...');
        updateSpoofModeVisibility(spoofMode);
      }, 500);
    }
  };

  // Debug function to inspect DOM structure
  const debugDropdownStructure = () => {
    console.log('=== DEBUGGING DROPDOWN STRUCTURE ===');

    // Look for all dropdown containers
    const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
    console.log('All dropdown containers found:', allDropdowns.length);
    allDropdowns.forEach(dropdown => {
      console.log('Dropdown ID:', dropdown.id, 'Visible:', dropdown.classList.contains('visible'));
    });

    // Check specifically for blur-info dropdown
    const blurInfoDropdown = document.getElementById('dropdown-blur-info');
    console.log('Blur info dropdown found:', !!blurInfoDropdown);
    if (!blurInfoDropdown) {
      console.warn('MISSING: dropdown-blur-info element not found in DOM!');

      // Try to find the blur-info item
      const blurInfoItem = document.querySelector('[data-item-id="blur-info"]');
      console.log('Blur info item found:', !!blurInfoItem);
      if (blurInfoItem) {
        console.log('Blur info item attributes:', {
          'data-has-dropdown': blurInfoItem.getAttribute('data-has-dropdown'),
          'data-expanded': blurInfoItem.getAttribute('data-expanded'),
          'data-item-id': blurInfoItem.getAttribute('data-item-id')
        });
      }
    }

    // Look specifically for name-spoofer dropdown
    const nameSpoofDropdown = document.getElementById('dropdown-name-spoofer');
    console.log('Name spoof dropdown:', nameSpoofDropdown);

    if (nameSpoofDropdown) {
      console.log('Name spoof dropdown children:', nameSpoofDropdown.children.length);
      Array.from(nameSpoofDropdown.children).forEach((child, index) => {
        console.log(`Child ${index}:`, child.tagName, child.id, child.getAttribute('data-item-id'));
      });
    }

    // Look for all items with data-item-id
    const allItems = document.querySelectorAll('[data-item-id]');
    console.log('All items with data-item-id:', allItems.length);
    allItems.forEach(item => {
      console.log('Item:', item.getAttribute('data-item-id'), 'Display:', item.style.display);
    });

    console.log('=== END DEBUG ===');
  };

  // Helper function to apply blur to elements
  const applyBlurToElements = (selector, intensity, doc = document) => {
    console.log('applyBlurToElements called with selector:', selector, 'intensity:', intensity);

    // Sanitize intensity value to ensure it's valid for CSS class names
    const sanitizedIntensity = sanitizeBlurIntensity(intensity);
    console.log('Sanitized intensity:', sanitizedIntensity);

    const elements = doc.querySelectorAll(selector);
    console.log('Found elements:', elements.length);

    elements.forEach((el, index) => {
      if (index < 3) { // Log first 3 elements for debugging
        console.log('Applying blur to element:', el.tagName, el.className, el.textContent?.substring(0, 30));
      }
      // Remove existing blur classes
      el.classList.remove('edvinity-blur-low', 'edvinity-blur-medium', 'edvinity-blur-high');
      // Add new blur class with sanitized intensity
      el.classList.add(`edvinity-blur-${sanitizedIntensity}`);
    });

    // If we're working with the main document, also apply to iframes
    if (doc === document) {
      applyToIframes((iframe) => {
        try {
          const iframeElements = iframe.contentDocument.querySelectorAll(selector);
          iframeElements.forEach(el => {
            // Remove existing blur classes
            el.classList.remove('edvinity-blur-low', 'edvinity-blur-medium', 'edvinity-blur-high');
            // Add new blur class with sanitized intensity
            el.classList.add(`edvinity-blur-${sanitizedIntensity}`);
          });
        } catch (e) {
          console.warn('Could not access iframe content:', e);
        }
      });
    }
  };

  // Helper function to remove blur from elements
  const removeBlurFromElements = (selector, doc = document) => {
    const elements = doc.querySelectorAll(selector);
    elements.forEach(el => {
      el.classList.remove('edvinity-blur-low', 'edvinity-blur-medium', 'edvinity-blur-high');
    });

    // If we're working with the main document, also remove from iframes
    if (doc === document) {
      applyToIframes((iframe) => {
        try {
          const iframeElements = iframe.contentDocument.querySelectorAll(selector);
          iframeElements.forEach(el => {
            el.classList.remove('edvinity-blur-low', 'edvinity-blur-medium', 'edvinity-blur-high');
          });
        } catch (e) {
          console.warn('Could not access iframe content:', e);
        }
      });
    }
  };



  // Helper function to apply a function to all iframes
  const applyToIframes = (callback) => {
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach(iframe => {
      // Only process same-origin iframes
      try {
        if (iframe.contentDocument) {
          callback(iframe);
        }
      } catch (e) {
        // Cross-origin iframe, can't access
        console.warn('Could not access iframe content (cross-origin):', e);
      }
    });
  };

  // Function to observe DOM changes for blur functionality
  const observeDOM = () => {
    // Create a MutationObserver to watch for DOM changes
    const observer = new MutationObserver((mutations) => {
      // Check if blur is enabled
      if (privacySettings['blur-info']) {
        // Process each mutation
        mutations.forEach(mutation => {
          // Handle added nodes
          if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            mutation.addedNodes.forEach(node => {
              // Only process element nodes
              if (node.nodeType === 1) {
                // Check if the node is an iframe
                if (node.tagName === 'IFRAME') {
                  // Handle the new iframe
                  if (!node.hasAttribute('data-edvinity-blur-handled')) {
                    // Mark this iframe as handled
                    node.setAttribute('data-edvinity-blur-handled', 'true');

                    // Add load event listener
                    node.addEventListener('load', () => {
                      if (privacySettings['blur-info']) {
                        try {
                          // Apply blur to iframe content
                          if (node.contentDocument) {
                            // Inject blur styles into the iframe
                            const blurStyles = `
                              .edvinity-blur-low {
                                filter: blur(3px);
                                transition: filter 0.3s ease;
                              }

                              .edvinity-blur-medium {
                                filter: blur(6px);
                                transition: filter 0.3s ease;
                              }

                              .edvinity-blur-high {
                                filter: blur(10px);
                                transition: filter 0.3s ease;
                              }
                            `;

                            // Create style element in iframe
                            try {
                              const styleEl = node.contentDocument.createElement('style');
                              styleEl.textContent = blurStyles;
                              node.contentDocument.head.appendChild(styleEl);
                            } catch (styleError) {
                              console.warn('Could not inject styles into iframe:', styleError);
                            }

                            // Apply blur to elements in the iframe based on sub-option settings
                            // Note: We don't blur names when name-spoofer is enabled because name-spoofer replaces text instead
                            // if (privacySettings['name-spoofer']) {
                            //   // Name spoofing replaces text, doesn't blur it
                            // }
                            if (privacySettings['blur-profile-pics']) {
                              applyBlurToElements(SELECTORS.PROFILE_PICS.join(','), BLUR_INTENSITY, node.contentDocument);
                            }
                            if (privacySettings['blur-student-ids']) {
                              applyBlurToElements(SELECTORS.STUDENT_INFO.join(','), BLUR_INTENSITY, node.contentDocument);
                            }
                          }
                        } catch (e) {
                          console.warn('Could not access iframe content (cross-origin):', e);
                        }
                      }
                    });
                  }
                }

                // Check if the node matches any of our selectors
                const matchesName = SELECTORS.NAMES.some(selector => {
                  try {
                    return node.matches && node.matches(selector);
                  } catch (e) {
                    return false;
                  }
                });

                const matchesPic = SELECTORS.PROFILE_PICS.some(selector => {
                  try {
                    return node.matches && node.matches(selector);
                  } catch (e) {
                    return false;
                  }
                });

                const matchesInfo = SELECTORS.STUDENT_INFO.some(selector => {
                  try {
                    return node.matches && node.matches(selector);
                  } catch (e) {
                    return false;
                  }
                });

                // Apply name spoofing to new elements if enabled
                if (matchesName && privacySettings['name-spoofer']) {
                  // Apply name spoofing to this element
                  const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
                  const spoofMode = blurSettings['spoof-mode'] || 'preset';
                  const presetName = blurSettings['preset-names'] || 'student';
                  const customName = blurSettings['custom-name'] || 'Anonymous';

                  let replacementText;
                  if (spoofMode === 'custom') {
                    replacementText = customName || 'Anonymous';
                  } else {
                    const presetReplacements = {
                      'student': 'Student',
                      'user': 'User',
                      'anonymous': 'Anonymous',
                      'redacted': '[REDACTED]'
                    };
                    replacementText = presetReplacements[presetName] || 'Student';
                  }

                  // Apply spoofing to the element
                  if (!node.hasAttribute('data-original-text') && node.textContent.trim()) {
                    node.setAttribute('data-original-text', node.textContent);
                    node.textContent = replacementText;
                    node.classList.add('edvinity-spoofed-name');
                  }
                }

                // Apply blur based on sub-option settings
                const sanitizedIntensity = sanitizeBlurIntensity(BLUR_INTENSITY);
                if (matchesPic && privacySettings['blur-profile-pics']) {
                  // Only blur profile pics if sub-option is enabled
                  node.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                }
                if (matchesInfo && privacySettings['blur-student-ids']) {
                  // Only blur student info if sub-option is enabled
                  node.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                }

                // Also check children of the node
                if (node.querySelectorAll) {
                  // Apply name spoofing to children if enabled
                  if (privacySettings['name-spoofer']) {
                    const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
                    const spoofMode = blurSettings['spoof-mode'] || 'preset';
                    const presetName = blurSettings['preset-names'] || 'student';
                    const customName = blurSettings['custom-name'] || 'Anonymous';

                    let replacementText;
                    if (spoofMode === 'custom') {
                      replacementText = customName || 'Anonymous';
                    } else {
                      const presetReplacements = {
                        'student': 'Student',
                        'user': 'User',
                        'anonymous': 'Anonymous',
                        'redacted': '[REDACTED]'
                      };
                      replacementText = presetReplacements[presetName] || 'Student';
                    }

                    SELECTORS.NAMES.forEach(selector => {
                      try {
                        node.querySelectorAll(selector).forEach(el => {
                          if (!el.hasAttribute('data-original-text') && el.textContent.trim()) {
                            el.setAttribute('data-original-text', el.textContent);
                            el.textContent = replacementText;
                            el.classList.add('edvinity-spoofed-name');
                          }
                        });
                      } catch (e) {}
                    });
                  }

                  // Only check profile pics if sub-option is enabled
                  if (privacySettings['blur-profile-pics']) {
                    SELECTORS.PROFILE_PICS.forEach(selector => {
                      try {
                        node.querySelectorAll(selector).forEach(el => {
                          el.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                        });
                      } catch (e) {}
                    });
                  }

                  // Only check student info if sub-option is enabled
                  if (privacySettings['blur-student-ids']) {
                    SELECTORS.STUDENT_INFO.forEach(selector => {
                      try {
                        node.querySelectorAll(selector).forEach(el => {
                          el.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                        });
                      } catch (e) {}
                    });
                  }
                }
              }
            });
          }
        });
      }
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    });

    return observer;
  };

  // Initialize privacy shield with actual functionality
  const initPrivacyShield = () => {
    // Load privacy settings from localStorage
    try {
      const savedSettings = JSON.parse(localStorage.getItem('edvinity-privacy-settings') || '{}');

      // Update privacy settings with saved values
      Object.keys(savedSettings).forEach(key => {
        if (key in privacySettings) {
          privacySettings[key] = savedSettings[key];
        }
      });

      // Create a function to update toggle states in the UI
      const updateToggleState = (settingId, isActive) => {
        const toggle = document.querySelector(`.edvinity-toggle[data-setting-id="${settingId}"]`);
        if (toggle) {
          // Update the toggle state based on the isActive parameter
          if (isActive) {
            toggle.classList.add('active');
            toggle.setAttribute('data-value', 'true');

            // Update the handle position
            const handle = toggle.querySelector('.edvinity-toggle-handle');
            if (handle) {
              handle.style.transform = 'translate(26px, -50%)';
            }
          } else {
            toggle.classList.remove('active');
            toggle.setAttribute('data-value', 'false');

            // Reset the handle position
            const handle = toggle.querySelector('.edvinity-toggle-handle');
            if (handle) {
              handle.style.transform = '';
            }
          }
        }
      };

      // Function to update all toggle states in the UI
      const updateAllToggleStates = () => {
        // Update all toggles based on their current settings
        Object.keys(privacySettings).forEach(key => {
          updateToggleState(key, privacySettings[key]);
        });
      };

      // Set up an observer to catch when the panel is created
      const setupToggleObserver = () => {
        const toggleObserver = new MutationObserver((mutations) => {
          for (const mutation of mutations) {
            if (mutation.addedNodes && mutation.addedNodes.length) {
              for (const node of mutation.addedNodes) {
                if (node.nodeType === 1 && (node.classList?.contains('edvinity-panel') || node.querySelector?.('.edvinity-panel'))) {
                  // Panel has been added, update toggle states
                  setTimeout(() => {
                    // Update all toggles
                    updateAllToggleStates();
                  }, 100); // Small delay to ensure toggles are rendered
                  toggleObserver.disconnect(); // No need to observe anymore
                  break;
                }
              }
            }
          }
        });

        // Start observing
        toggleObserver.observe(document.body, {
          childList: true,
          subtree: true
        });
      };

      // Apply functionality based on saved settings
      if (privacySettings['blur-info']) {
        console.log('Initializing blur with saved settings:', privacySettings);
        toggleBlurInfo(true);
      }

      if (privacySettings['name-spoofer']) {
        // Load name spoofer settings and apply
        const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
        const spoofMode = blurSettings['spoof-mode'] || 'preset';
        const presetName = blurSettings['preset-names'] || 'student';
        const customName = blurSettings['custom-name'] || 'Anonymous';

        applyNameSpoofing(true, spoofMode, spoofMode === 'custom' ? customName : presetName);
      }

      // Set initial visibility for spoof mode fields
      setTimeout(() => {
        const blurSettings = JSON.parse(localStorage.getItem('edvinity-blur-settings') || '{}');
        const spoofMode = blurSettings['spoof-mode'] || 'preset';
        console.log('Setting initial spoof mode visibility:', spoofMode);
        updateSpoofModeVisibility(spoofMode);
      }, 1500); // Increased timeout to ensure DOM is fully ready

      if (privacySettings['anti-logout']) {
        toggleAntiLogout(true);
      }

      if (privacySettings['auto-mute']) {
        toggleAutoMute(true);
      }

      // Try to update immediately if the panel is already created
      updateAllToggleStates();

      // Set up observer to catch when the panel is created
      setupToggleObserver();

      // Update toggle states for sub-options after a delay to ensure DOM is ready
      setTimeout(() => {
        console.log('Updating sub-option toggle states...');
        Object.keys(privacySettings).forEach(settingId => {
          const toggle = document.querySelector(`.edvinity-toggle[data-setting-id="${settingId}"]`);
          if (toggle) {
            const isActive = privacySettings[settingId];
            console.log(`Setting toggle ${settingId} to ${isActive}`);
            if (isActive) {
              toggle.classList.add('active');
              toggle.setAttribute('data-value', 'true');
            } else {
              toggle.classList.remove('active');
              toggle.setAttribute('data-value', 'false');
            }
          }
        });
      }, 1000);
    } catch (e) {
      console.warn('Could not load privacy settings:', e);
    }

    // Start DOM observation to catch dynamically added elements
    const observer = observeDOM();

    // Handle iframe content when they load
    const handleIframes = () => {
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        // Skip iframes that already have a load event listener
        if (iframe.hasAttribute('data-edvinity-blur-handled')) {
          return;
        }

        // Mark this iframe as handled
        iframe.setAttribute('data-edvinity-blur-handled', 'true');

        // Try to access iframe content when it loads
        iframe.addEventListener('load', () => {
          if (privacySettings['blur-info']) {
            try {
              // Apply blur to iframe content
              if (iframe.contentDocument) {
                // Inject blur styles into the iframe
                const blurStyles = `
                  .edvinity-blur-low {
                    filter: blur(3px);
                    transition: filter 0.3s ease;
                  }

                  .edvinity-blur-medium {
                    filter: blur(6px);
                    transition: filter 0.3s ease;
                  }

                  .edvinity-blur-high {
                    filter: blur(10px);
                    transition: filter 0.3s ease;
                  }
                `;

                // Create style element in iframe
                try {
                  const styleEl = iframe.contentDocument.createElement('style');
                  styleEl.textContent = blurStyles;
                  iframe.contentDocument.head.appendChild(styleEl);
                } catch (styleError) {
                  console.warn('Could not inject styles into iframe:', styleError);
                }

                // Apply blur to elements in the iframe based on sub-option settings
                if (privacySettings['name-spoofer']) {
                  applyBlurToElements(SELECTORS.NAMES.join(','), BLUR_INTENSITY, iframe.contentDocument);
                }
                if (privacySettings['blur-profile-pics']) {
                  applyBlurToElements(SELECTORS.PROFILE_PICS.join(','), BLUR_INTENSITY, iframe.contentDocument);
                }
                if (privacySettings['blur-student-ids']) {
                  applyBlurToElements(SELECTORS.STUDENT_INFO.join(','), BLUR_INTENSITY, iframe.contentDocument);
                }

                // Set up an observer for the iframe content
                try {
                  const iframeObserver = new MutationObserver((mutations) => {
                    if (privacySettings['blur-info']) {
                      mutations.forEach(mutation => {
                        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                          mutation.addedNodes.forEach(node => {
                            if (node.nodeType === 1) {
                              // Apply blur to the node based on sub-option settings
                              const sanitizedIntensity = sanitizeBlurIntensity(BLUR_INTENSITY);
                              // Note: We don't blur names when name-spoofer is enabled because name-spoofer replaces text instead
                              // if (privacySettings['name-spoofer']) {
                              //   // Name spoofing replaces text, doesn't blur it
                              // }

                              // Only apply profile pic blur if sub-option is enabled
                              if (privacySettings['blur-profile-pics']) {
                                SELECTORS.PROFILE_PICS.forEach(selector => {
                                  try {
                                    if (node.matches && node.matches(selector)) {
                                      node.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                                    }

                                    // Also check children
                                    if (node.querySelectorAll) {
                                      node.querySelectorAll(selector).forEach(el => {
                                        el.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                                      });
                                    }
                                  } catch (e) {}
                                });
                              }

                              // Only apply student info blur if sub-option is enabled
                              if (privacySettings['blur-student-ids']) {
                                SELECTORS.STUDENT_INFO.forEach(selector => {
                                  try {
                                    if (node.matches && node.matches(selector)) {
                                      node.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                                    }

                                    // Also check children
                                    if (node.querySelectorAll) {
                                      node.querySelectorAll(selector).forEach(el => {
                                        el.classList.add(`edvinity-blur-${sanitizedIntensity}`);
                                      });
                                    }
                                  } catch (e) {}
                                });
                              }
                            }
                          });
                        }
                      });
                    }
                  });

                  // Start observing the iframe document
                  iframeObserver.observe(iframe.contentDocument.body, {
                    childList: true,
                    subtree: true,
                    attributes: false,
                    characterData: false
                  });

                  // Store the observer to prevent garbage collection
                  if (!window.edvinityIframeObservers) {
                    window.edvinityIframeObservers = [];
                  }
                  window.edvinityIframeObservers.push(iframeObserver);
                } catch (observerError) {
                  console.warn('Could not set up observer for iframe:', observerError);
                }
              }
            } catch (e) {
              console.warn('Could not access iframe content (cross-origin):', e);
            }
          }
        });
      });
    };

    // Handle iframes initially and when DOM changes
    handleIframes();

    // Set up a periodic check for iframes (for dynamic content)
    const iframeCheckInterval = setInterval(handleIframes, 2000);

    // Store the interval ID to prevent garbage collection
    window.edvinityIframeCheckInterval = iframeCheckInterval;

    // Only keep dropdown visibility initialization for UI
    setTimeout(() => {
      // Make sure all dropdowns are closed by default
      document.querySelectorAll('.edvinity-dropdown-content').forEach(dropdown => {
        dropdown.classList.remove('visible');
      });

      document.querySelectorAll('.edvinity-item[data-has-dropdown="true"]').forEach(item => {
        item.setAttribute('data-expanded', 'false');
      });
    }, 500); // Small delay to ensure DOM is ready

    return observer;
  };

  // Apply UI settings changes - UI only, no functionality
  const applyUISettings = (settingId, value) => {
    // No functionality, just UI
    // No saving settings
    // No theme changes
  };

  // Load saved settings - UI only, no functionality
  const loadSavedSettings = () => {
    // No loading of settings
    // No applying settings
    // Just keep the function as a stub for UI
  };

  // --- Site Compatibility Check ---
  // We're now using @exclude in the userscript header instead of runtime checks

  // --- Safe Initialization ---
  const safeInit = () => {
    try {
      // Check if the document has a body
      if (!document.body) {
        return;
      }

      // If we're in an iframe, only apply blur functionality without creating UI
      if (isInIframe) {
        // Only load and apply privacy settings for blur functionality
        try {
          // Get settings from localStorage (shared across all frames in the same domain)
          const savedSettings = JSON.parse(localStorage.getItem('edvinity-privacy-settings') || '{}');

          // Update privacy settings with saved values
          Object.keys(savedSettings).forEach(key => {
            if (key in privacySettings) {
              privacySettings[key] = savedSettings[key];
            }
          });

          // Apply blur if it was enabled
          if (privacySettings['blur-info']) {
            // Inject only the blur styles
            const blurStyles = `
              .edvinity-blur-low {
                filter: blur(3px);
                transition: filter 0.3s ease;
              }

              .edvinity-blur-medium {
                filter: blur(6px);
                transition: filter 0.3s ease;
              }

              .edvinity-blur-high {
                filter: blur(10px);
                transition: filter 0.3s ease;
              }
            `;

            // Inject minimal styles
            const styleEl = document.createElement('style');
            styleEl.textContent = blurStyles;
            document.head.appendChild(styleEl);

            // Apply blur to elements in this iframe
            toggleBlurInfo(true);

            // Set up observer for dynamic content
            const observer = observeDOM();
            window.edvinityObserver = observer;
          }

          // Apply anti-logout if it was enabled
          if (privacySettings['anti-logout']) {
            toggleAntiLogout(true);
          }

          // Apply auto-mute if it was enabled
          if (privacySettings['auto-mute']) {
            toggleAutoMute(true);
          }

          // Set up a listener for storage changes to sync settings across frames
          window.addEventListener('storage', (event) => {
            if (event.key === 'edvinity-privacy-settings') {
              try {
                const newSettings = JSON.parse(event.newValue || '{}');

                // Update settings and apply functionality for each changed setting
                Object.keys(newSettings).forEach(key => {
                  if (key in privacySettings && newSettings[key] !== privacySettings[key]) {
                    // Update the setting value
                    privacySettings[key] = newSettings[key];

                    // Apply the functionality
                    if (key === 'blur-info') {
                      toggleBlurInfo(newSettings[key]);
                    } else if (key === 'anti-logout') {
                      toggleAntiLogout(newSettings[key]);
                    } else if (key === 'auto-mute') {
                      toggleAutoMute(newSettings[key]);
                    }
                  }
                });
              } catch (e) {
                console.warn('Could not process storage event:', e);
              }
            }
          });
        } catch (e) {
          console.warn('Could not load privacy settings in iframe:', e);
        }
      } else {
        // In main window - setup the full UI
        setupUI();

        // Initialize privacy shield with actual functionality
        const observer = initPrivacyShield();

        // Store the observer in a global variable to prevent garbage collection
        window.edvinityObserver = observer;
      }
    } catch (error) {
      console.warn('Error during initialization:', error);
    }
  };

  // --- Initialization ---
  // Ensure DOM is ready before setup
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', safeInit);
  } else {
    safeInit();
  }

})();